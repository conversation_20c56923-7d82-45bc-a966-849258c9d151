/**
 * @fileoverview Organization Invitation Management API Routes
 *
 * This module provides comprehensive invitation management functionality for organizations,
 * handling the complete invitation lifecycle from validation to acceptance/rejection.
 *
 * **Main Functionalities:**
 * - Public invitation validation (no authentication required)
 * - Authenticated invitation details retrieval
 * - Invitation acceptance with automatic membership creation
 * - Invitation rejection handling
 *
 * **Architecture Context:**
 * This API integrates with the organization membership system and handles both public
 * (unauthenticated) and private (authenticated) invitation operations. It works in
 * conjunction with the members API for creating invitations and the auth system for
 * user verification.
 *
 * **Related Systems:**
 * - Organization membership management (`apps/backend/src/routes/members.ts`)
 * - Authentication system (`packages/auth/`)
 * - Email notification system (`packages/email/`)
 * - Frontend invitation handling (`apps/dashboard/src/features/auth/components/`)
 *
 * @see {@link file://./members.ts} - For invitation creation endpoints
 * @see {@link file://../../AUTHENTICATION_FLOWS.md} - For complete auth flow documentation
 * @see {@link file://../../../dashboard/src/features/auth/components/invitation-handler.tsx} - Frontend invitation UI
 */

import { createRoute, OpenAPIHono, z } from "@hono/zod-openapi";
import { prisma } from "@repo/database/client";

import { schemas, type Variables } from "../lib/openapi.js";

/**
 * **Invitation Validation Response Schema**
 *
 * Defines the structure for public invitation validation responses.
 * Used by the validate invitation endpoint to provide basic invitation
 * status information without requiring authentication.
 *
 * **Used By:**
 * - `/invitations/{id}/validate` endpoint (public access)
 * - `apps/dashboard/src/lib/api/invitations.ts` - `validateInvitation()` function
 * - `apps/dashboard/src/app/accept-invitation/[id]/page.tsx` - Pre-auth validation
 *
 * **Validation Rules:**
 * - `valid`: Boolean indicating if invitation exists and is pending
 * - `message`: Human-readable status description
 * - `email`: Invitation target email (only returned if valid)
 * - `organizationName`: Organization name (only returned if valid)
 * - `inviterName`: Person who sent invitation (only returned if valid)
 */
const invitationValidationSchema = z.object({
  valid: z.boolean(),
  message: z.string().optional(),
  email: z.string().optional(),
  organizationName: z.string().optional(),
  inviterName: z.string().optional()
});

/**
 * **Invitation Details Response Schema**
 *
 * Comprehensive invitation information schema for authenticated requests.
 * Contains full invitation metadata including expiration, organization details,
 * and security-sensitive information.
 *
 * **Used By:**
 * - `/invitations/{id}` endpoint (authenticated access)
 * - `apps/dashboard/src/lib/api/invitations.ts` - `getInvitation()` function
 * - `apps/dashboard/src/features/auth/components/invitation-handler.tsx` - Detailed invitation display
 *
 * **Security Notes:**
 * - Only accessible to users whose email matches the invitation email
 * - Contains sensitive organization and inviter information
 * - Includes calculated expiration timestamp (7 days from creation)
 */
const invitationDetailsSchema = z.object({
  id: z.string(),
  organizationName: z.string(),
  organizationSlug: z.string(),
  inviterEmail: z.string(),
  email: z.string(),
  role: z.enum(["viewer", "contributor", "admin", "owner"]),
  status: z.enum(["pending", "accepted", "rejected", "revoked", "expired"]),
  expiresAt: z.string().datetime(),
  organizationId: z.string(),
  inviterId: z.string()
});

/**
 * **Invitation Action Response Schema**
 *
 * Standard response format for invitation actions (accept/reject).
 * Provides consistent success/failure messaging across all invitation
 * state-changing operations.
 *
 * **Used By:**
 * - `/invitations/{id}/accept` endpoint
 * - `/invitations/{id}/reject` endpoint
 * - `apps/dashboard/src/lib/api/invitations.ts` - Action response handlers
 * - `apps/dashboard/src/features/auth/components/invitation-handler.tsx` - Action feedback
 */
const invitationResponseSchema = z.object({
  success: z.boolean(),
  message: z.string()
});

/**
 * **Invitations API Router**
 *
 * OpenAPI Hono router instance for all invitation-related endpoints.
 * Configured with proper type inference for request/response validation.
 */
export const invitationsRouter = new OpenAPIHono<{ Variables: Variables }>();

/**
 * **Validate Invitation Route Definition**
 *
 * **Flow Context:**
 * This is the first step in the invitation acceptance flow. It's called when users
 * click invitation links to verify the invitation exists and is still valid before
 * requiring authentication. This enables proper UX flow where invalid invitations
 * don't force users through unnecessary sign-up/sign-in processes.
 *
 * **Security Features:**
 * - **Public endpoint** - No authentication required for basic validation
 * - **Limited data exposure** - Only returns essential invitation info
 * - **Status checking** - Validates invitation is still in "pending" state
 * - **No sensitive data** - Organization/inviter names only, no IDs or tokens
 *
 * **Related Components:**
 * - `apps/dashboard/src/app/accept-invitation/[id]/page.tsx` - Server-side validation
 * - `apps/dashboard/src/lib/api/invitations.ts` - Client-side API wrapper
 * - `apps/dashboard/src/features/auth/components/invitation-handler.tsx` - UI handling
 *
 * @see {@link file://../../../dashboard/src/app/accept-invitation/[id]/page.tsx} - Page that uses this validation
 */
const validateInvitationRoute = createRoute({
  method: "get",
  path: "/invitations/{id}/validate",
  tags: ["Invitations"],
  summary: "Validate invitation",
  description:
    "Publicly validate if an invitation exists and is valid (without authentication)",
  request: {
    params: z.object({
      id: z.string().min(1)
    })
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: invitationValidationSchema
        }
      },
      description: "Invitation validation result"
    },
    404: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Invitation not found"
    }
  }
});

/**
 * **Validate Invitation Handler**
 *
 * **Purpose:**
 * Provides public validation of invitation links without requiring authentication.
 * This is the entry point for invitation flows, allowing users to verify an
 * invitation exists and is valid before proceeding with sign-up or sign-in.
 *
 * **Used By:**
 * - `apps/dashboard/src/app/accept-invitation/[id]/page.tsx` - Server-side pre-validation
 * - `apps/dashboard/src/lib/api/invitations.ts` - `validateInvitation()` function
 * - `apps/dashboard/src/features/auth/components/invitation-handler.tsx` - Client-side validation
 *
 * **Process:**
 * 1. Extract invitation ID from URL parameters
 * 2. Query database for invitation with organization and inviter details
 * 3. Validate invitation exists and is in "pending" status
 * 4. Return validation result with minimal necessary information
 * 5. Handle errors gracefully without exposing system details
 *
 * **Security:**
 * - **No authentication required** - Public endpoint for initial validation
 * - **Limited data exposure** - Only returns non-sensitive invitation details
 * - **Input validation** - ID parameter validated through OpenAPI schema
 * - **Error handling** - Graceful failure without system information leakage
 * - **Status verification** - Only pending invitations considered valid
 *
 * **Returns:**
 * - Success case: `{ valid: true, message: string, email: string, organizationName: string, inviterName: string }`
 * - Invalid invitation: `{ valid: false, message: string }` (invitation not found, expired, etc.)
 * - Error case: `{ valid: false, message: "Error validating invitation" }`
 *
 * @param {Context} c - Hono context object containing request parameters
 * @returns {Promise<Response>} JSON response with invitation validation status
 */
invitationsRouter.openapi(validateInvitationRoute, async (c) => {
  const { id } = c.req.param();

  try {
    const invitation = await prisma.invitation.findUnique({
      where: { id },
      select: {
        id: true,
        status: true,
        email: true,
        Organization: {
          select: {
            name: true
          }
        },
        invitedByUser: {
          select: {
            name: true,
            email: true
          }
        }
      }
    });

    if (!invitation) {
      return c.json(
        {
          valid: false,
          message: "Invitation not found"
        },
        200
      );
    }

    // Check if invitation is still valid
    const isNotPending = invitation.status !== "pending";

    if (isNotPending) {
      return c.json(
        {
          valid: false,
          message: `Invitation is ${invitation.status}`
        },
        200
      );
    }

    return c.json(
      {
        valid: true,
        message: "Invitation is valid",
        email: invitation.email,
        organizationName: invitation.Organization.name,
        inviterName:
          invitation.invitedByUser.name ||
          invitation.invitedByUser.email ||
          "Unknown"
      },
      200
    );
  } catch (error) {
    console.error("Error validating invitation:", error);
    return c.json(
      {
        valid: false,
        message: "Error validating invitation"
      },
      200
    );
  }
});

/**
 * **Get Invitation Details Route Definition**
 *
 * **Flow Context:**
 * This endpoint provides detailed invitation information for authenticated users.
 * It's called after initial validation when users need complete invitation data
 * to make informed decisions about accepting or rejecting the invitation.
 *
 * **Security Features:**
 * - **Authentication required** - User must be signed in to access details
 * - **Email matching** - User's email must match invitation target email
 * - **Complete data access** - Returns full invitation metadata including expiration
 * - **Organization details** - Includes organization name and slug for navigation
 *
 * **Related Components:**
 * - `apps/dashboard/src/lib/api/invitations.ts` - `getInvitation()` function
 * - `apps/dashboard/src/features/auth/components/invitation-handler.tsx` - Detailed invitation UI
 *
 * @see {@link file://../../../dashboard/src/features/auth/components/invitation-handler.tsx} - Component that displays these details
 */
const getInvitationRoute = createRoute({
  method: "get",
  path: "/invitations/{id}",
  tags: ["Invitations"],
  summary: "Get invitation details",
  description:
    "Get detailed information about an invitation (requires authentication)",
  request: {
    params: z.object({
      id: z.string().min(1)
    })
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: invitationDetailsSchema
        }
      },
      description: "Invitation details retrieved successfully"
    },
    401: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Unauthorized"
    },
    404: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Invitation not found"
    },
    403: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Invitation email does not match user email"
    },
    500: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Internal server error"
    }
  }
});

/**
 * **Get Invitation Details Handler**
 *
 * **Purpose:**
 * Retrieves comprehensive invitation information for authenticated users.
 * This endpoint provides all necessary data for users to understand the
 * invitation details before making accept/reject decisions.
 *
 * **Used By:**
 * - `apps/dashboard/src/lib/api/invitations.ts` - `getInvitation()` function
 * - `apps/dashboard/src/features/auth/components/invitation-handler.tsx` - Invitation detail display
 *
 * **Process:**
 * 1. Verify user authentication from session context
 * 2. Query invitation with organization and inviter details
 * 3. Validate invitation exists in database
 * 4. Enforce email matching security check (invitation.email === user.email)
 * 5. Calculate invitation expiration date (7 days from creation)
 * 6. Return complete invitation metadata with sensitive details
 *
 * **Security:**
 * - **Authentication required** - User must have valid session
 * - **Email matching enforcement** - User email must match invitation target
 * - **Complete data access** - Returns all invitation metadata including IDs
 * - **Input validation** - ID parameter validated through OpenAPI schema
 * - **Error handling** - Specific error codes for different failure scenarios
 *
 * **Returns:**
 * - Success case: Complete invitation details including organization info, role, expiration
 * - Unauthorized: `{ error: "Authentication required", timestamp: string }` (401)
 * - Not found: `{ error: "Invitation not found", timestamp: string }` (404)
 * - Forbidden: `{ error: "Invitation email does not match user email", timestamp: string }` (403)
 * - Server error: `{ error: "Failed to fetch invitation details", timestamp: string }` (500)
 *
 * @param {Context} c - Hono context object with authenticated user and request parameters
 * @returns {Promise<Response>} JSON response with detailed invitation information
 */
// @ts-expect-error Complex Hono OpenAPI type inference issue - runtime behavior is correct
invitationsRouter.openapi(getInvitationRoute, async (c) => {
  const user = c.get("user");
  const { id } = c.req.param();

  if (!user?.id) {
    return c.json(
      {
        error: "Authentication required",
        timestamp: new Date().toISOString()
      },
      401
    );
  }

  try {
    const invitation = await prisma.invitation.findUnique({
      where: { id },
      include: {
        Organization: {
          select: {
            name: true,
            slug: true
          }
        },
        invitedByUser: {
          select: {
            email: true
          }
        }
      }
    });

    if (!invitation) {
      return c.json(
        {
          error: "Invitation not found",
          timestamp: new Date().toISOString()
        },
        404
      );
    }

    // Check if invitation email matches user email
    if (invitation.email !== user.email) {
      return c.json(
        {
          error: "Invitation email does not match user email",
          timestamp: new Date().toISOString()
        },
        403
      );
    }

    // Calculate expiration (invitations expire after 7 days)
    const expiresAt = new Date(invitation.createdAt);
    expiresAt.setDate(expiresAt.getDate() + 7);

    return c.json(
      {
        id: invitation.id,
        organizationName: invitation.Organization.name,
        organizationSlug: invitation.Organization.slug,
        inviterEmail: invitation.invitedByUser.email || "Unknown",
        email: invitation.email,
        role: invitation.role,
        status: invitation.status,
        expiresAt: expiresAt.toISOString(),
        organizationId: invitation.organizationId,
        inviterId: invitation.invitedByUserId
      },
      200
    );
  } catch (error) {
    console.error("Error fetching invitation details:", error);
    return c.json(
      {
        error: "Failed to fetch invitation details",
        timestamp: new Date().toISOString()
      },
      500
    );
  }
});

/**
 * **Accept Invitation Route Definition**
 *
 * **Flow Context:**
 * This is the primary action endpoint for invitation acceptance. When users decide
 * to join an organization, this endpoint handles all the complex logic of creating
 * memberships, updating user roles, and managing invitation state transitions.
 *
 * **Security Features:**
 * - **Authentication required** - User must be signed in to accept invitations
 * - **Email verification** - User email must match invitation target email
 * - **Status validation** - Only pending invitations can be accepted
 * - **Expiration checking** - Automatic expiration after 7 days
 * - **Transaction safety** - Database operations are atomic
 *
 * **Related Components:**
 * - `apps/dashboard/src/lib/api/invitations.ts` - `acceptInvitation()` function
 * - `apps/dashboard/src/features/auth/components/invitation-handler.tsx` - Accept button handler
 * - `apps/dashboard/src/lib/api/onboarding.ts` - Post-acceptance onboarding updates
 *
 * @see {@link file://../../../dashboard/src/features/auth/components/invitation-handler.tsx} - UI component for acceptance
 */
const acceptInvitationRoute = createRoute({
  method: "post",
  path: "/invitations/{id}/accept",
  tags: ["Invitations"],
  summary: "Accept invitation",
  description: "Accept an organization invitation (requires authentication)",
  request: {
    params: z.object({
      id: z.string().min(1)
    })
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: invitationResponseSchema
        }
      },
      description: "Invitation accepted successfully"
    },
    401: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Unauthorized"
    },
    404: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Invitation not found"
    },
    400: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Invitation is not pending or has expired"
    },
    403: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Invitation email does not match user email"
    },
    500: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Internal server error"
    }
  }
});

/**
 * **Accept Invitation Handler**
 *
 * **Purpose:**
 * Processes invitation acceptance by creating organization membership and updating
 * user permissions. This is the core endpoint that transforms invitations into
 * active organization memberships with proper role assignment.
 *
 * **Used By:**
 * - `apps/dashboard/src/lib/api/invitations.ts` - `acceptInvitation()` function
 * - `apps/dashboard/src/features/auth/components/invitation-handler.tsx` - Accept button functionality
 * - Frontend flows after user authentication and invitation validation
 *
 * **Process:**
 * 1. Verify user authentication and extract invitation ID
 * 2. Fetch invitation with organization details from database
 * 3. Validate invitation exists and is accessible by current user
 * 4. Enforce email matching security (invitation.email === user.email)
 * 5. Check invitation status is "pending" (not already processed)
 * 6. Verify invitation hasn't expired (7-day TTL from creation)
 * 7. Execute atomic transaction:
 *    - Update invitation status to "accepted"
 *    - Create organization membership with invited role
 *    - Update user's system role based on organization role
 *    - Skip membership creation if user is already a member
 * 8. Return success confirmation
 *
 * **Security:**
 * - **Authentication required** - Valid user session mandatory
 * - **Email matching** - Prevents unauthorized invitation acceptance
 * - **Status validation** - Only pending invitations can be accepted
 * - **Expiration enforcement** - Automatic expiration after 7 days
 * - **Atomic operations** - Database transaction ensures consistency
 * - **Duplicate prevention** - Checks for existing membership before creation
 *
 * **Returns:**
 * - Success case: `{ success: true, message: "Invitation accepted successfully" }`
 * - Authentication error: `{ error: "Authentication required", timestamp: string }` (401)
 * - Not found: `{ error: "Invitation not found", timestamp: string }` (404)
 * - Email mismatch: `{ error: "Invitation email does not match user email", timestamp: string }` (403)
 * - Invalid status: `{ error: "Invitation is [status]", timestamp: string }` (400)
 * - Expired: `{ error: "Invitation has expired", timestamp: string }` (400)
 * - Server error: `{ error: "Failed to accept invitation", timestamp: string }` (500)
 *
 * @param {Context} c - Hono context object with authenticated user and request parameters
 * @returns {Promise<Response>} JSON response indicating acceptance success or failure reason
 */
invitationsRouter.openapi(acceptInvitationRoute, async (c) => {
  const user = c.get("user");
  const { id } = c.req.param();

  if (!user?.id) {
    return c.json(
      {
        error: "Authentication required",
        timestamp: new Date().toISOString()
      },
      401
    );
  }

  try {
    const invitation = await prisma.invitation.findUnique({
      where: { id },
      include: {
        Organization: {
          select: {
            id: true,
            name: true,
            slug: true
          }
        }
      }
    });

    if (!invitation) {
      return c.json(
        {
          error: "Invitation not found",
          timestamp: new Date().toISOString()
        },
        404
      );
    }

    // Check if invitation email matches user email
    if (invitation.email !== user.email) {
      return c.json(
        {
          error: "Invitation email does not match user email",
          timestamp: new Date().toISOString()
        },
        403
      );
    }

    // Check if invitation is still pending
    if (invitation.status !== "pending") {
      return c.json(
        {
          error: `Invitation is ${invitation.status}`,
          timestamp: new Date().toISOString()
        },
        400
      );
    }

    // Check if invitation has expired (7 days)
    const expiresAt = new Date(invitation.createdAt);
    expiresAt.setDate(expiresAt.getDate() + 7);

    if (new Date() > expiresAt) {
      await prisma.invitation.update({
        where: { id },
        data: { status: "expired" }
      });

      return c.json(
        {
          error: "Invitation has expired",
          timestamp: new Date().toISOString()
        },
        400
      );
    }

    // Accept invitation in transaction
    await prisma.$transaction(async (tx: typeof prisma) => {
      // Update invitation status
      await tx.invitation.update({
        where: { id },
        data: { status: "accepted" }
      });

      // Check if user is already a member
      const existingMembership = await tx.membership.findFirst({
        where: {
          userId: user.id,
          organizationId: invitation.organizationId
        }
      });

      if (!existingMembership) {
        // Create membership
        await tx.membership.create({
          data: {
            userId: user.id,
            organizationId: invitation.organizationId,
            role: invitation.role
          }
        });

        // Update user's system role if needed
        const userRole = ["viewer", "contributor"].includes(invitation.role)
          ? "user"
          : "admin";

        await tx.user.update({
          where: { id: user.id },
          data: { role: userRole }
        });
      }
    });

    return c.json(
      {
        success: true,
        message: "Invitation accepted successfully"
      },
      200
    );
  } catch (error) {
    console.error("Error accepting invitation:", error);
    return c.json(
      {
        error: "Failed to accept invitation",
        timestamp: new Date().toISOString()
      },
      500
    );
  }
});

/**
 * **Reject Invitation Route Definition**
 *
 * **Flow Context:**
 * This endpoint allows users to formally decline organization invitations.
 * It provides a clean way to close the invitation loop and update the
 * invitation status without creating any organizational relationships.
 *
 * **Security Features:**
 * - **Authentication required** - User must be signed in to reject invitations
 * - **Email verification** - User email must match invitation target email
 * - **Status validation** - Only pending invitations can be rejected
 * - **Simple state update** - Only changes invitation status, no complex operations
 *
 * **Related Components:**
 * - `apps/dashboard/src/lib/api/invitations.ts` - `rejectInvitation()` function
 * - `apps/dashboard/src/features/auth/components/invitation-handler.tsx` - Reject button handler
 *
 * @see {@link file://../../../dashboard/src/features/auth/components/invitation-handler.tsx} - UI component for rejection
 */
const rejectInvitationRoute = createRoute({
  method: "post",
  path: "/invitations/{id}/reject",
  tags: ["Invitations"],
  summary: "Reject invitation",
  description: "Reject an organization invitation (requires authentication)",
  request: {
    params: z.object({
      id: z.string().min(1)
    })
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: invitationResponseSchema
        }
      },
      description: "Invitation rejected successfully"
    },
    401: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Unauthorized"
    },
    404: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Invitation not found"
    },
    400: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Invitation is not pending"
    },
    403: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Invitation email does not match user email"
    },
    500: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Internal server error"
    }
  }
});

/**
 * **Reject Invitation Handler**
 *
 * **Purpose:**
 * Allows authenticated users to formally decline organization invitations.
 * This provides a clean closure to the invitation process and updates the
 * invitation status to prevent future acceptance attempts.
 *
 * **Used By:**
 * - `apps/dashboard/src/lib/api/invitations.ts` - `rejectInvitation()` function
 * - `apps/dashboard/src/features/auth/components/invitation-handler.tsx` - Reject button functionality
 * - Frontend flows when users choose to decline invitations
 *
 * **Process:**
 * 1. Verify user authentication and extract invitation ID
 * 2. Fetch invitation record from database
 * 3. Validate invitation exists and is accessible
 * 4. Enforce email matching security (invitation.email === user.email)
 * 5. Check invitation status is "pending" (not already processed)
 * 6. Update invitation status to "rejected"
 * 7. Return success confirmation
 *
 * **Security:**
 * - **Authentication required** - Valid user session mandatory
 * - **Email matching** - Prevents unauthorized invitation rejection
 * - **Status validation** - Only pending invitations can be rejected
 * - **Simple operation** - No complex state changes or side effects
 * - **Input validation** - ID parameter validated through OpenAPI schema
 *
 * **Returns:**
 * - Success case: `{ success: true, message: "Invitation rejected successfully" }`
 * - Authentication error: `{ error: "Authentication required", timestamp: string }` (401)
 * - Not found: `{ error: "Invitation not found", timestamp: string }` (404)
 * - Email mismatch: `{ error: "Invitation email does not match user email", timestamp: string }` (403)
 * - Invalid status: `{ error: "Invitation is [status]", timestamp: string }` (400)
 * - Server error: `{ error: "Failed to reject invitation", timestamp: string }` (500)
 *
 * @param {Context} c - Hono context object with authenticated user and request parameters
 * @returns {Promise<Response>} JSON response indicating rejection success or failure reason
 */
invitationsRouter.openapi(rejectInvitationRoute, async (c) => {
  const user = c.get("user");
  const { id } = c.req.param();

  if (!user?.id) {
    return c.json(
      {
        error: "Authentication required",
        timestamp: new Date().toISOString()
      },
      401
    );
  }

  try {
    const invitation = await prisma.invitation.findUnique({
      where: { id }
    });

    if (!invitation) {
      return c.json(
        {
          error: "Invitation not found",
          timestamp: new Date().toISOString()
        },
        404
      );
    }

    // Check if invitation email matches user email
    if (invitation.email !== user.email) {
      return c.json(
        {
          error: "Invitation email does not match user email",
          timestamp: new Date().toISOString()
        },
        403
      );
    }

    // Check if invitation is still pending
    if (invitation.status !== "pending") {
      return c.json(
        {
          error: `Invitation is ${invitation.status}`,
          timestamp: new Date().toISOString()
        },
        400
      );
    }

    // Reject invitation
    await prisma.invitation.update({
      where: { id },
      data: { status: "rejected" }
    });

    return c.json(
      {
        success: true,
        message: "Invitation rejected successfully"
      },
      200
    );
  } catch (error) {
    console.error("Error rejecting invitation:", error);
    return c.json(
      {
        error: "Failed to reject invitation",
        timestamp: new Date().toISOString()
      },
      500
    );
  }
});
