/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { createRoute, OpenAPIHono, z } from "@hono/zod-openapi";
import { prisma } from "@repo/database/client";

import { schemas, type Variables } from "../lib/openapi.js";
import {
  deleteFile,
  deleteOldWorkspaceLogo,
  generatePresignedUploadUrl,
  isAllowedFileType,
  isValidFileSize,
  isValidS3LogoUrl
} from "../lib/s3.js";

// Organization schema
const organizationSchema = z.object({
  id: z.string(),
  name: z.string(),
  slug: z.string(),
  logo: z.string().nullable(),
  createdAt: z.string().datetime(),
  metadata: z.string().nullable()
});

// Organization member schema
// organizationMemberSchema moved to members.ts

// Organization with user role schema
const organizationWithRoleSchema = organizationSchema.extend({
  userRole: z.enum(["viewer", "contributor", "admin", "owner"])
});

// Create organizations router
export const organizationsRouter = new OpenAPIHono<{ Variables: Variables }>();

// Get organization by slug
const getOrganizationRoute = createRoute({
  method: "get",
  path: "/organizations/{slug}",
  tags: ["Organizations"],
  summary: "Get organization by slug",
  description: "Get organization details by slug with user's role",
  request: {
    params: z.object({
      slug: z.string().min(1).max(50)
    })
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: organizationWithRoleSchema
        }
      },
      description: "Organization found"
    },
    401: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Unauthorized"
    },
    403: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Not a member of this organization"
    },
    404: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Organization not found"
    }
  }
});

organizationsRouter.openapi(getOrganizationRoute, async (c) => {
  const user = c.get("user");
  const { slug } = c.req.param();

  if (!user?.id) {
    return c.json(
      {
        error: "Authentication required",
        timestamp: new Date().toISOString()
      },
      401
    );
  }

  try {
    // Find organization and check user membership
    const organization = await prisma.organization.findUnique({
      where: { slug },
      include: {
        memberships: {
          where: { userId: user.id },
          select: { role: true }
        }
      }
    });

    if (!organization) {
      return c.json(
        {
          error: "Organization not found",
          timestamp: new Date().toISOString()
        },
        404
      );
    }

    // Check if user is a member
    const membership = organization.memberships[0];
    if (!membership) {
      return c.json(
        {
          error: "You are not a member of this organization",
          timestamp: new Date().toISOString()
        },
        403
      );
    }

    // Return organization with user's role
    const { memberships, ...orgData } = organization;
    return c.json(
      {
        ...orgData,
        userRole: membership.role,
        createdAt: organization.createdAt.toISOString()
      },
      200
    );
  } catch (error) {
    console.error("Error fetching organization:", error);
    throw error;
  }
});

// Members endpoint moved to /routes/members.ts for better organization and status computation

// Update organization (admin/owner only)
const updateOrganizationRoute = createRoute({
  method: "put",
  path: "/organizations/{slug}",
  tags: ["Organizations"],
  summary: "Update organization",
  description: "Update organization details (admin/owner only)",
  request: {
    params: z.object({
      slug: z.string().min(1).max(50)
    }),
    body: {
      content: {
        "application/json": {
          schema: z.object({
            name: z.string().min(1).max(100).optional(),
            logo: z.string().url().nullable().optional(),
            metadata: z.string().nullable().optional()
          })
        }
      }
    }
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: organizationSchema
        }
      },
      description: "Organization updated"
    },
    401: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Unauthorized"
    },
    403: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Insufficient permissions"
    },
    404: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Organization not found"
    }
  }
});

organizationsRouter.openapi(updateOrganizationRoute, async (c) => {
  const user = c.get("user");
  const { slug } = c.req.param();
  const body = c.req.valid("json");

  if (!user?.id) {
    return c.json(
      {
        error: "Authentication required",
        timestamp: new Date().toISOString()
      },
      401
    );
  }

  try {
    // Find organization and check user permissions
    const organization = await prisma.organization.findUnique({
      where: { slug },
      include: {
        memberships: {
          where: { userId: user.id },
          select: { role: true }
        }
      }
    });

    if (!organization) {
      return c.json(
        {
          error: "Organization not found",
          timestamp: new Date().toISOString()
        },
        404
      );
    }

    const membership = organization.memberships[0];
    if (!membership) {
      return c.json(
        {
          error: "You are not a member of this organization",
          timestamp: new Date().toISOString()
        },
        403
      );
    }

    // Check if user has admin or owner role
    if (!["admin", "owner"].includes(membership.role)) {
      return c.json(
        {
          error: "Only admins and owners can update the organization",
          timestamp: new Date().toISOString()
        },
        403
      );
    }

    // Update organization
    const updatedOrganization = await prisma.organization.update({
      where: { id: organization.id },
      data: {
        ...(body.name && { name: body.name }),
        ...(body.logo !== undefined && { logo: body.logo }),
        ...(body.metadata !== undefined && { metadata: body.metadata })
      }
    });

    return c.json(
      {
        ...updatedOrganization,
        createdAt: updatedOrganization.createdAt.toISOString()
      },
      200
    );
  } catch (error) {
    console.error("Error updating organization:", error);
    throw error;
  }
});

// Check if organization slug is available (onboarding workspace)
const checkSlugRoute = createRoute({
  method: "post",
  path: "/organization/check-slug",
  tags: ["Organizations"],
  summary: "Check if organization slug is available",
  description: "Check if an organization slug is available for use",
  request: {
    body: {
      content: {
        "application/json": {
          schema: z.object({
            slug: z.string().min(3).max(48)
          })
        }
      }
    }
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string().optional()
          })
        }
      },
      description: "Slug availability status"
    },
    401: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Unauthorized"
    },
    500: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Internal server error"
    }
  }
});

organizationsRouter.openapi(checkSlugRoute, async (c) => {
  const user = c.get("user");
  const body = c.req.valid("json");

  if (!user?.id) {
    return c.json(
      {
        error: "Authentication required",
        timestamp: new Date().toISOString()
      },
      401
    );
  }

  try {
    // Check if an organization with this slug already exists
    const existingOrg = await prisma.organization.findUnique({
      where: { slug: body.slug }
    });

    return c.json(
      {
        status: !existingOrg, // true if available, false if taken
        message: existingOrg ? "Slug is already taken" : "Slug is available"
      },
      200
    );
  } catch (error) {
    console.error("Error checking slug:", error);
    return c.json(
      {
        error: "Failed to check slug availability",
        timestamp: new Date().toISOString()
      },
      500
    );
  }
});

// Create organization
const createOrganizationRoute = createRoute({
  method: "post",
  path: "/organization/create",
  tags: ["Organizations"],
  summary: "Create a new organization",
  description: "Create a new organization with the authenticated user as owner",
  request: {
    body: {
      content: {
        "application/json": {
          schema: z.object({
            name: z.string().min(1).max(100),
            slug: z
              .string()
              .min(3)
              .max(48)
              .regex(/^[a-z0-9]+(?:-[a-z0-9]+)*$/),
            logo: z.string().url().optional(),
            metadata: z.string().optional()
          })
        }
      }
    }
  },
  responses: {
    201: {
      content: {
        "application/json": {
          schema: organizationSchema
        }
      },
      description: "Organization created successfully"
    },
    400: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Invalid request or slug already taken"
    },
    401: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Unauthorized"
    },
    409: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Organization with this slug already exists"
    },
    500: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Internal server error"
    }
  }
});

// @ts-expect-error Complex Hono OpenAPI type inference issue - runtime behavior is correct
organizationsRouter.openapi(createOrganizationRoute, async (c) => {
  const user = c.get("user");
  const body = c.req.valid("json");

  if (!user?.id) {
    return c.json(
      {
        error: "Authentication required",
        timestamp: new Date().toISOString()
      },
      401
    );
  }

  try {
    // Use transaction to create organization + member atomically
    // This reduces database round-trips and ensures data consistency
    const organization = await prisma.$transaction(
      async (tx: typeof prisma) => {
        // Check if slug is already taken (within transaction for consistency)
        const existingOrg = await tx.organization.findUnique({
          where: { slug: body.slug }
        });

        if (existingOrg) {
          throw new Error("SLUG_ALREADY_EXISTS");
        }

        // Update user role to admin when creating organization
        await tx.user.update({
          where: { id: user.id },
          data: { role: "admin" }
        });

        // Create organization with nested membership creation in a single operation
        const newOrganization = await tx.organization.create({
          data: {
            name: body.name,
            slug: body.slug,
            logo: body.logo || null,
            metadata: body.metadata || null,
            // Create the owner membership in the same transaction
            memberships: {
              create: {
                userId: user.id!,
                role: "owner"
              }
            }
          },
          // Include the membership data in the response to verify creation
          include: {
            memberships: {
              where: { userId: user.id },
              select: { role: true, createdAt: true }
            }
          }
        });

        // Set this organization as the active organization in the user's session
        await tx.session.updateMany({
          where: { 
            userId: user.id,
            expires: { gt: new Date() } // Only update non-expired sessions
          },
          data: { activeOrganizationId: newOrganization.id }
        });

        return newOrganization;
      }
    );

    return c.json(
      {
        id: organization.id,
        name: organization.name,
        slug: organization.slug,
        logo: organization.logo,
        metadata: organization.metadata,
        createdAt: organization.createdAt.toISOString()
      } satisfies {
        id: string;
        name: string;
        slug: string;
        logo: string | null;
        metadata: string | null;
        createdAt: string;
      },
      201
    );
  } catch (error: any) {
    console.error("Error creating organization:", error);

    // Handle specific error cases
    if (error instanceof Error && error.message === "SLUG_ALREADY_EXISTS") {
      return c.json(
        {
          error: "An organization with this slug already exists",
          timestamp: new Date().toISOString()
        },
        409
      );
    }

    // Handle Prisma unique constraint violations (race condition fallback)
    if (error?.code === "P2002" && error?.meta?.target?.includes("slug")) {
      return c.json(
        {
          error: "An organization with this slug already exists",
          timestamp: new Date().toISOString()
        },
        409
      );
    }

    return c.json(
      {
        error: "Failed to create organization",
        timestamp: new Date().toISOString()
      },
      500
    );
  }
});

// Generate presigned URL for workspace logo upload
const generateLogoUploadUrlRoute = createRoute({
  method: "post",
  path: "/organizations/{slug}/upload-logo",
  tags: ["Organizations"],
  summary: "Generate presigned URL for workspace logo upload",
  description:
    "Generate a presigned URL for uploading workspace logo (admin/owner only)",
  request: {
    params: z.object({
      slug: z.string().min(1).max(50)
    }),
    body: {
      content: {
        "application/json": {
          schema: z.object({
            fileName: z.string().min(1).max(255),
            fileType: z.string().regex(/^image\/(png|jpeg|jpg|gif)$/),
            fileSize: z
              .number()
              .int()
              .positive()
              .max(10 * 1024 * 1024) // 10MB
          })
        }
      }
    }
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            uploadUrl: z.string().url(),
            fileUrl: z.string().url(),
            key: z.string()
          })
        }
      },
      description: "Presigned URL generated successfully"
    },
    400: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Invalid file type or size"
    },
    401: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Unauthorized"
    },
    403: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Insufficient permissions"
    },
    404: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Organization not found"
    },
    500: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Internal server error"
    }
  }
});

organizationsRouter.openapi(generateLogoUploadUrlRoute, async (c) => {
  const user = c.get("user");
  const { slug } = c.req.param();
  const { fileName, fileType, fileSize } = c.req.valid("json");

  if (!user?.id) {
    return c.json(
      {
        error: "Authentication required",
        timestamp: new Date().toISOString()
      },
      401
    );
  }

  try {
    // Find organization and check user permissions
    const organization = await prisma.organization.findUnique({
      where: { slug },
      include: {
        memberships: {
          where: { userId: user.id },
          select: { role: true }
        }
      }
    });

    if (!organization) {
      return c.json(
        {
          error: "Organization not found",
          timestamp: new Date().toISOString()
        },
        404
      );
    }

    const membership = organization.memberships[0];
    if (!membership) {
      return c.json(
        {
          error: "You are not a member of this organization",
          timestamp: new Date().toISOString()
        },
        403
      );
    }

    // Check if user has admin or owner role
    if (!["admin", "owner"].includes(membership.role)) {
      return c.json(
        {
          error: "Only admins and owners can upload workspace logos",
          timestamp: new Date().toISOString()
        },
        403
      );
    }

    // Validate file type and size
    if (!isAllowedFileType(fileType)) {
      return c.json(
        {
          error: "File type not allowed. Allowed types: PNG, JPEG, GIF",
          timestamp: new Date().toISOString()
        },
        400
      );
    }

    if (!isValidFileSize(fileSize)) {
      return c.json(
        {
          error: "File size must be between 1 byte and 10MB",
          timestamp: new Date().toISOString()
        },
        400
      );
    }

    // Generate presigned URL
    const result = await generatePresignedUploadUrl(
      organization.id,
      fileName,
      fileType,
      fileSize
    );

    return c.json(result, 200);
  } catch (error: any) {
    console.error("Error generating presigned URL:", error);
    return c.json(
      {
        error: error.message || "Failed to generate upload URL",
        timestamp: new Date().toISOString()
      },
      500
    );
  }
});

// Update workspace logo after successful upload
const updateWorkspaceLogoRoute = createRoute({
  method: "patch",
  path: "/organizations/{slug}/logo",
  tags: ["Organizations"],
  summary: "Update workspace logo URL",
  description:
    "Update workspace logo URL after successful S3 upload (admin/owner only)",
  request: {
    params: z.object({
      slug: z.string().min(1).max(50)
    }),
    body: {
      content: {
        "application/json": {
          schema: z.object({
            logoUrl: z.string().url()
          })
        }
      }
    }
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: organizationSchema
        }
      },
      description: "Logo updated successfully"
    },
    400: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Invalid logo URL"
    },
    401: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Unauthorized"
    },
    403: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Insufficient permissions"
    },
    404: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Organization not found"
    },
    500: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Internal server error"
    }
  }
});

organizationsRouter.openapi(updateWorkspaceLogoRoute, async (c) => {
  const user = c.get("user");
  const { slug } = c.req.param();
  const { logoUrl } = c.req.valid("json");

  if (!user?.id) {
    return c.json(
      {
        error: "Authentication required",
        timestamp: new Date().toISOString()
      },
      401
    );
  }

  try {
    // Find organization and check user permissions
    const organization = await prisma.organization.findUnique({
      where: { slug },
      include: {
        memberships: {
          where: { userId: user.id },
          select: { role: true }
        }
      }
    });

    if (!organization) {
      return c.json(
        {
          error: "Organization not found",
          timestamp: new Date().toISOString()
        },
        404
      );
    }

    const membership = organization.memberships[0];
    if (!membership) {
      return c.json(
        {
          error: "You are not a member of this organization",
          timestamp: new Date().toISOString()
        },
        403
      );
    }

    // Check if user has admin or owner role
    if (!["admin", "owner"].includes(membership.role)) {
      return c.json(
        {
          error: "Only admins and owners can update workspace logos",
          timestamp: new Date().toISOString()
        },
        403
      );
    }

    // Validate that the logo URL belongs to our S3 bucket
    if (!isValidS3LogoUrl(logoUrl)) {
      return c.json(
        {
          error:
            "Invalid logo URL. Logo must be uploaded through the proper upload endpoint.",
          timestamp: new Date().toISOString()
        },
        400
      );
    }

    // Delete old logo if it exists
    if (organization.logo) {
      await deleteOldWorkspaceLogo(organization.logo);
    }

    // Update organization logo
    const updatedOrganization = await prisma.organization.update({
      where: { id: organization.id },
      data: { logo: logoUrl }
    });

    return c.json(
      {
        ...updatedOrganization,
        createdAt: updatedOrganization.createdAt.toISOString()
      },
      200
    );
  } catch (error: any) {
    console.error("Error updating workspace logo:", error);
    return c.json(
      {
        error: error.message || "Failed to update workspace logo",
        timestamp: new Date().toISOString()
      },
      500
    );
  }
});

// Delete workspace logo
const deleteWorkspaceLogoRoute = createRoute({
  method: "delete",
  path: "/organizations/{slug}/logo",
  tags: ["Organizations"],
  summary: "Delete workspace logo",
  description: "Delete workspace logo and remove from S3 (admin/owner only)",
  request: {
    params: z.object({
      slug: z.string().min(1).max(50)
    })
  },
  responses: {
    204: {
      description: "Logo deleted successfully"
    },
    401: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Unauthorized"
    },
    403: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Insufficient permissions"
    },
    404: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Organization not found"
    },
    500: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Internal server error"
    }
  }
});

organizationsRouter.openapi(deleteWorkspaceLogoRoute, async (c) => {
  const user = c.get("user");
  const { slug } = c.req.param();

  if (!user?.id) {
    return c.json(
      {
        error: "Authentication required",
        timestamp: new Date().toISOString()
      },
      401
    );
  }

  try {
    // Find organization and check user permissions
    const organization = await prisma.organization.findUnique({
      where: { slug },
      include: {
        memberships: {
          where: { userId: user.id },
          select: { role: true }
        }
      }
    });

    if (!organization) {
      return c.json(
        {
          error: "Organization not found",
          timestamp: new Date().toISOString()
        },
        404
      );
    }

    const membership = organization.memberships[0];
    if (!membership) {
      return c.json(
        {
          error: "You are not a member of this organization",
          timestamp: new Date().toISOString()
        },
        403
      );
    }

    // Check if user has admin or owner role
    if (!["admin", "owner"].includes(membership.role)) {
      return c.json(
        {
          error: "Only admins and owners can delete workspace logos",
          timestamp: new Date().toISOString()
        },
        403
      );
    }

    // Delete logo from S3 if it exists
    if (organization.logo) {
      await deleteFile(organization.logo);
    }

    // Update organization to remove logo
    await prisma.organization.update({
      where: { id: organization.id },
      data: { logo: null }
    });

    return c.body(null, 204);
  } catch (error: any) {
    console.error("Error deleting workspace logo:", error);
    return c.json(
      {
        error: error.message || "Failed to delete workspace logo",
        timestamp: new Date().toISOString()
      },
      500
    );
  }
});

// Delete organization (owner only)
const deleteOrganizationRoute = createRoute({
  method: "delete",
  path: "/organizations/{slug}",
  tags: ["Organizations"],
  summary: "Delete organization",
  description:
    "Permanently delete organization and all associated data (owner only)",
  request: {
    params: z.object({
      slug: z.string().min(1).max(50)
    }),
    body: {
      content: {
        "application/json": {
          schema: z.object({
            confirmationSlug: z.string().min(1),
            reason: z.enum([
              "no-longer-needed",
              "switching-providers",
              "cost-concerns",
              "feature-limitations",
              "poor-performance",
              "security-concerns",
              "other"
            ]),
            feedback: z.string().max(1000).optional()
          })
        }
      }
    }
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            success: z.boolean(),
            remainingWorkspaces: z.array(
              z.object({
                slug: z.string(),
                name: z.string()
              })
            ),
            wasDefaultWorkspace: z.boolean()
          })
        }
      },
      description: "Organization deleted successfully with redirection data"
    },
    400: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Invalid confirmation slug or missing data"
    },
    401: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Unauthorized"
    },
    403: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Only organization owners can delete organizations"
    },
    404: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Organization not found"
    },
    500: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Internal server error"
    }
  }
});

// @ts-expect-error Complex Hono OpenAPI type inference issue - runtime behavior is correct
organizationsRouter.openapi(deleteOrganizationRoute, async (c) => {
  const user = c.get("user");
  if (!user?.id) {
    return c.json(
      { error: "Authentication required", timestamp: new Date().toISOString() },
      401
    );
  }

  const { slug } = c.req.param();
  const { confirmationSlug, reason, feedback } = c.req.valid("json");

  // Validate confirmation slug matches
  if (confirmationSlug !== slug) {
    return c.json(
      {
        error: "Confirmation slug does not match organization slug",
        timestamp: new Date().toISOString()
      },
      400
    );
  }

  try {
    // Get full user information including defaultWorkspace
    const fullUser = await prisma.user.findUnique({
      where: { id: user.id },
      select: { id: true, defaultWorkspace: true }
    });

    if (!fullUser) {
      return c.json(
        { error: "User not found", timestamp: new Date().toISOString() },
        404
      );
    }

    // Find organization and verify ownership
    const organization = await prisma.organization.findFirst({
      where: { slug },
      include: {
        memberships: {
          where: { userId: user.id },
          select: { role: true }
        }
      }
    });

    if (!organization) {
      return c.json(
        {
          error: "Organization not found",
          timestamp: new Date().toISOString()
        },
        404
      );
    }

    const userMembership = organization.memberships[0];
    if (!userMembership || userMembership.role !== "owner") {
      return c.json(
        {
          error: "Only organization owners can delete organizations",
          timestamp: new Date().toISOString()
        },
        403
      );
    }

    // Get user's remaining organizations before deletion
    const remainingOrganizations = await prisma.organization.findMany({
      where: {
        id: { not: organization.id },
        memberships: {
          some: { userId: user.id }
        }
      },
      select: {
        id: true,
        slug: true,
        name: true,
        createdAt: true
      },
      orderBy: { createdAt: "asc" }
    });

    const wasDefaultWorkspace = fullUser.defaultWorkspace === slug;

    // Log deletion event for analytics
    console.log(
      `Organization deletion: ${slug} by user ${user.id} - Reason: ${reason}${feedback ? ` | Feedback: ${feedback}` : ""}`
    );

    // Clean up S3 resources if logo exists
    if (organization.logo) {
      try {
        await deleteFile(organization.logo);
      } catch (error) {
        console.error("Failed to delete organization logo from S3:", error);
        // Continue with deletion even if S3 cleanup fails
      }
    }

    // Clean up users' defaultWorkspace if it matches this organization
    await prisma.user.updateMany({
      where: { defaultWorkspace: slug },
      data: { defaultWorkspace: null }
    });

    // Delete organization (cascades to members, invitations, todos)
    await prisma.organization.delete({
      where: { id: organization.id }
    });

    // If this was the user's last workspace, revoke all their sessions
    // and reset their onboarding status to prevent redirect loops
    if (remainingOrganizations.length === 0) {
      try {
        // Reset onboarding status to "workspace" so they go through onboarding again
        await prisma.user.update({
          where: { id: user.id },
          data: { onboardingStatus: "workspace" }
        });

        // Revoke all sessions for this user to force them to sign up again
        await prisma.session.updateMany({
          where: { userId: user.id },
          data: { activeOrganizationId: null }
        });

        console.log(
          `Reset onboarding status and revoked all sessions for user ${user.id} (last workspace deleted)`
        );
      } catch (error) {
        console.error(
          "Failed to revoke user sessions or reset onboarding:",
          error
        );
        // Continue with deletion even if session cleanup fails
      }
    }

    // Return redirection information
    return c.json(
      {
        success: true,
        remainingWorkspaces: remainingOrganizations.map(
          (org: { slug: string; name: string }) => ({
            slug: org.slug,
            name: org.name
          })
        ),
        wasDefaultWorkspace
      } satisfies {
        success: boolean;
        remainingWorkspaces: { name: string; slug: string }[];
        wasDefaultWorkspace: boolean;
      },
      200
    );
  } catch (error) {
    console.error("Error deleting organization:", error);
    return c.json(
      {
        error: "Failed to delete organization",
        timestamp: new Date().toISOString()
      },
      500
    );
  }
});
