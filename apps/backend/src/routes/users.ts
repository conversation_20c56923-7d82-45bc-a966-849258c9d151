import { create<PERSON>oute, OpenAPIHono, z } from "@hono/zod-openapi";
import { prisma } from "@repo/database/client";

import type { OnboardingStatus } from "../../../../packages/database/generated/prisma/index.js";
import { schemas, type Variables } from "../lib/openapi.js";
import { notFound } from "../middleware/error-handler.js";

// User schema - updated to match Prisma User model
const userSchema = z.object({
  id: z.string(), // ULID format, not UUID
  email: z.string().email().nullable(),
  name: z.string().nullable(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime()
});

// Create users router
export const usersRouter = new OpenAPIHono<{ Variables: Variables }>();

// List users
const listUsersRoute = createRoute({
  method: "get",
  path: "/users",
  tags: ["Users"],
  summary: "List all users",
  description: "Get a paginated list of users",
  request: {
    query: z.object({
      page: z.coerce.number().int().positive().default(1).optional(),
      pageSize: z.coerce
        .number()
        .int()
        .positive()
        .max(100)
        .default(10)
        .optional(),
      search: z.string().optional()
    })
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            users: z.array(userSchema),
            pagination: schemas.pagination
          })
        }
      },
      description: "List of users"
    }
  }
});

usersRouter.openapi(listUsersRoute, async (c) => {
  const query = c.req.query();
  const page = Number(query.page) || 1;
  const pageSize = Number(query.pageSize) || 10;

  // TODO: Implement actual database query this is a placeholder
  const users = [
    {
      id: "123e4567-e89b-12d3-a456-************",
      email: "<EMAIL>",
      name: "Example User" as string | null,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  ];

  return c.json(
    {
      users,
      pagination: {
        page,
        pageSize,
        total: users.length,
        totalPages: Math.ceil(users.length / pageSize)
      }
    },
    200
  );
});

// Get user by ID
const getUserRoute = createRoute({
  method: "get",
  path: "/users/{id}",
  tags: ["Users"],
  summary: "Get user by ID",
  request: {
    params: z.object({
      id: z.string() // ULID format, not UUID
    })
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: userSchema
        }
      },
      description: "User found"
    },
    404: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "User not found"
    }
  }
});

usersRouter.openapi(getUserRoute, async (c) => {
  const { id } = c.req.param();

  // TODO: Implement actual database query
  if (id === "123e4567-e89b-12d3-a456-************") {
    return c.json(
      {
        id,
        email: "<EMAIL>",
        name: "Example User" as string | null,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      200
    );
  }

  return notFound("User not found");
});

// Create user
const createUserRoute = createRoute({
  method: "post",
  path: "/users",
  tags: ["Users"],
  summary: "Create a new user",
  request: {
    body: {
      content: {
        "application/json": {
          schema: z.object({
            email: z.string().email(),
            name: z.string().optional(),
            password: z.string().min(8)
          })
        }
      }
    }
  },
  responses: {
    201: {
      content: {
        "application/json": {
          schema: userSchema
        }
      },
      description: "User created"
    },
    400: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Validation error"
    },
    409: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "User already exists"
    }
  }
});

usersRouter.openapi(createUserRoute, async (c) => {
  const body = c.req.valid("json");

  // !: Implement actual user creation
  return c.json(
    {
      id: "123e4567-e89b-12d3-a456-************",
      email: body.email,
      name: body.name || null,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    201
  );
});

// Get current user profile (protected route)
const getProfileRoute = createRoute({
  method: "get",
  path: "/profile",
  tags: ["Users"],
  summary: "Get current user profile",
  description: "Get the authenticated user's profile information",
  responses: {
    200: {
      content: {
        "application/json": {
          schema: userSchema
        }
      },
      description: "User profile"
    },
    401: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Unauthorized"
    },
    404: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "User not found"
    }
  }
});

// @ts-expect-error Complex Hono OpenAPI type inference issue - runtime behavior is correct
usersRouter.openapi(getProfileRoute, async (c) => {
  const user = c.get("user");

  if (!user?.id) {
    return c.json(
      {
        error: "Unauthorized",
        timestamp: new Date().toISOString()
      },
      401
    );
  }

  // Fetch complete user data from database since context user doesn't have all fields
  const dbUser = await prisma.user.findUnique({
    where: { id: user.id },
    select: {
      id: true,
      email: true,
      name: true,
      createdAt: true,
      updatedAt: true
    }
  });

  if (!dbUser) {
    return c.json(
      {
        error: "User not found",
        timestamp: new Date().toISOString()
      },
      404
    );
  }

  return c.json(
    {
      id: dbUser.id,
      email: dbUser.email,
      name: dbUser.name,
      createdAt: dbUser.createdAt.toISOString(),
      updatedAt: dbUser.updatedAt.toISOString()
    },
    200
  );
});

// Update onboarding status
const updateOnboardingStatusRoute = createRoute({
  method: "patch",
  path: "/profile/onboarding-status",
  tags: ["Users"],
  summary: "Update user onboarding status",
  description: "Update the authenticated user's onboarding status",
  request: {
    body: {
      content: {
        "application/json": {
          schema: z.object({
            onboardingStatus: z.enum([
              "incomplete",
              "workspace",
              "invite",
              "complete"
            ])
          })
        }
      }
    }
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            success: z.boolean(),
            onboardingStatus: z.enum([
              "incomplete",
              "workspace",
              "invite",
              "complete"
            ])
          })
        }
      },
      description: "Onboarding status updated"
    },
    401: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Unauthorized"
    }
  }
});

usersRouter.openapi(updateOnboardingStatusRoute, async (c) => {
  const user = c.get("user");

  if (!user) {
    return c.json(
      {
        error: "Unauthorized",
        timestamp: new Date().toISOString()
      },
      401
    );
  }

  const { onboardingStatus } = c.req.valid("json");

  await prisma.user.update({
    where: { id: user.id },
    data: { onboardingStatus: onboardingStatus as OnboardingStatus }
  });

  return c.json(
    {
      success: true,
      onboardingStatus
    },
    200
  );
});

// Update default workspace
const updateDefaultWorkspaceRoute = createRoute({
  method: "patch",
  path: "/profile/default-workspace",
  tags: ["Users"],
  summary: "Update user default workspace",
  description: "Update the authenticated user's default workspace",
  request: {
    body: {
      content: {
        "application/json": {
          schema: z.object({
            defaultWorkspace: z.string().nullable()
          })
        }
      }
    }
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            success: z.boolean(),
            defaultWorkspace: z.string().nullable()
          })
        }
      },
      description: "Default workspace updated"
    },
    401: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Unauthorized"
    }
  }
});

usersRouter.openapi(updateDefaultWorkspaceRoute, async (c) => {
  const user = c.get("user");

  if (!user) {
    return c.json(
      {
        error: "Unauthorized",
        timestamp: new Date().toISOString()
      },
      401
    );
  }

  const { defaultWorkspace } = c.req.valid("json");

  await prisma.user.update({
    where: { id: user.id },
    data: { defaultWorkspace }
  });

  return c.json(
    {
      success: true,
      defaultWorkspace
    },
    200
  );
});

// Get user active organization from session
const getActiveOrganizationRoute = createRoute({
  method: "get",
  path: "/profile/active-organization",
  tags: ["Users"],
  summary: "Get user's active organization",
  description: "Get the authenticated user's active organization from their current session",
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            activeOrganization: z.object({
              id: z.string(),
              name: z.string(),
              slug: z.string(),
              logo: z.string().nullable(),
              userRole: z.enum(["viewer", "contributor", "admin", "owner"]),
              createdAt: z.string()
            }).nullable()
          })
        }
      },
      description: "Active organization retrieved"
    },
    401: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Unauthorized"
    }
  }
});

// @ts-expect-error Complex Hono OpenAPI type inference issue - runtime behavior is correct
usersRouter.openapi(getActiveOrganizationRoute, async (c) => {
  const user = c.get("user");

  if (!user) {
    return c.json(
      {
        error: "Unauthorized",
        timestamp: new Date().toISOString()
      },
      401
    );
  }

  // Get the user's current session to find activeOrganizationId
  const session = await prisma.session.findFirst({
    where: { 
      userId: user.id,
      expires: { gt: new Date() } // Only get non-expired sessions
    },
    orderBy: { updatedAt: "desc" }, // Get the most recent session
    select: { activeOrganizationId: true }
  });

  if (!session?.activeOrganizationId) {
    return c.json(
      {
        activeOrganization: null
      },
      200
    );
  }

  // Get the organization details with user's role
  const membership = await prisma.membership.findFirst({
    where: { 
      userId: user.id,
      organizationId: session.activeOrganizationId
    },
    include: {
      organization: {
        select: {
          id: true,
          name: true,
          slug: true,
          logo: true,
          createdAt: true
        }
      }
    }
  });

  if (!membership) {
    return c.json(
      {
        activeOrganization: null
      },
      200
    );
  }

  return c.json(
    {
      activeOrganization: {
        id: membership.organization.id,
        name: membership.organization.name,
        slug: membership.organization.slug,
        logo: membership.organization.logo,
        userRole: membership.role as "admin" | "contributor" | "owner" | "viewer",
        createdAt: membership.organization.createdAt.toISOString()
      }
    },
    200
  );
});

// Get user organizations
const getUserOrganizationsRoute = createRoute({
  method: "get",
  path: "/profile/organizations",
  tags: ["Users"],
  summary: "Get user organizations",
  description: "Get the authenticated user's organizations",
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            organizations: z.array(
              z.object({
                id: z.string(),
                name: z.string(),
                slug: z.string(),
                logo: z.string().nullable(),
                userRole: z.enum(["viewer", "contributor", "admin", "owner"]),
                createdAt: z.string()
              })
            )
          })
        }
      },
      description: "User organizations retrieved"
    },
    401: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Unauthorized"
    }
  }
});

// @ts-expect-error Complex Hono OpenAPI type inference issue - runtime behavior is correct
usersRouter.openapi(getUserOrganizationsRoute, async (c) => {
  const user = c.get("user");

  if (!user) {
    return c.json(
      {
        error: "Unauthorized",
        timestamp: new Date().toISOString()
      },
      401
    );
  }

  const organizations = await prisma.membership.findMany({
    where: { userId: user.id },
    include: {
      organization: {
        select: {
          id: true,
          name: true,
          slug: true,
          logo: true,
          createdAt: true
        }
      }
    },
    orderBy: {
      createdAt: "asc"
    }
  });

  const formattedOrganizations = organizations.map((membership: any) => ({
    id: membership.organization.id,
    name: membership.organization.name,
    slug: membership.organization.slug,
    logo: membership.organization.logo,
    userRole: membership.role as "admin" | "contributor" | "owner" | "viewer",
    createdAt: membership.organization.createdAt.toISOString()
  }));

  return c.json(
    {
      organizations: formattedOrganizations
    },
    200
  );
});
