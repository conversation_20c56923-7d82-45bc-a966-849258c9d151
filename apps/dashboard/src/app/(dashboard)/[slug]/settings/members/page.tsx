import { Metadata } from "next";
import { WorkspacePageWrapper } from "@/src/features/auth/components/workspace-page-wrapper";
import { getInitialMembersData } from "@/src/features/settings-members/dal/members";

import TeamMembersPageClient from "./team-members-page-client";

export const metadata: Metadata = {
  title: "Team Members Settings | Centaly"
};

interface PageProps {
  params: Promise<{ slug: string }>;
}

export default async function TeamMembersSettingsPage({ params }: PageProps) {
  const { slug } = await params;

  // Fetch initial members data
  const initialMembersData = await getInitialMembersData(slug);

  return (
    <WorkspacePageWrapper slug={slug}>
      <TeamMembersPageClient 
        initialData={initialMembersData.error ? undefined : {
          members: initialMembersData.members,
          pagination: initialMembersData.pagination
        }} 
      />
    </WorkspacePageWrapper>
  );
}
