import { Metadata } from "next";
import { WorkspacePageWrapper } from "@/src/features/auth/components/workspace-page-wrapper";

export const metadata: Metadata = {
  title: "Preferences Settings | Centaly"
};

interface PageProps {
  params: Promise<{ slug: string }>;
}

export default async function PreferencesSettingsPage({ params }: PageProps) {
  const { slug } = await params;

  return (
    <WorkspacePageWrapper slug={slug}>
      <div className="p-10">
        <h1>Preferences</h1>
      </div>
    </WorkspacePageWrapper>
  );
}
