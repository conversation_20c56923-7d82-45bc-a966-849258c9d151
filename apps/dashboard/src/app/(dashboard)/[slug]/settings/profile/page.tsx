import { Metadata } from "next";
import { WorkspacePageWrapper } from "@/src/features/auth/components/workspace-page-wrapper";

export const metadata: Metadata = {
  title: "Profile Settings | Centaly"
};

interface PageProps {
  params: Promise<{ slug: string }>;
}

export default async function ProfileSettingsPage({ params }: PageProps) {
  const { slug } = await params;

  return (
    <WorkspacePageWrapper slug={slug}>
      <div className="p-10">
        <h1>Profile Settings</h1>
      </div>
    </WorkspacePageWrapper>
  );
}
