import { Suspense } from "react";
import { <PERSON>ada<PERSON> } from "next";
import { WorkspaceGuard } from "@/src/features/auth/components/workspace-guard";

import { CreateTodoForm } from "../../../../features/todos/components/create-todo-form";
import { TodosList } from "../../../../features/todos/components/todos-list";
import { getTodos } from "../../../../features/todos/dal/todos";

interface TodosPageProps {
  params: Promise<{
    slug: string;
  }>;
  searchParams: Promise<{
    page?: string;
    completed?: string;
  }>;
}

export async function generateMetadata({
  params
}: {
  params: Promise<{ slug: string }>;
}): Promise<Metadata> {
  const { slug } = await params;

  return {
    title: `Todos - ${slug}`,
    description: "Manage your todos and track your progress"
  };
}

function TodosLoading() {
  return (
    <div className="space-y-6">
      <div className="h-12 bg-gray-100 animate-pulse rounded-lg" />
      <div className="space-y-3">
        {Array.from({ length: 5 }).map((_, i) => (
          <div
            key={i}
            className="h-16 bg-gray-100 animate-pulse rounded-lg"
          />
        ))}
      </div>
    </div>
  );
}

async function TodosContent({
  slug,
  searchParams
}: {
  slug: string;
  searchParams: { page?: string; completed?: string };
}) {
  // Get workspace data from the session provider (already verified by WorkspacePageWrapper)
  const { currentWorkspace } = await requireWorkspaceAccess(slug);

  // Parse search params
  const page = searchParams.page ? parseInt(searchParams.page, 10) : 1;
  const completed =
    searchParams.completed === "true"
      ? true
      : searchParams.completed === "false"
        ? false
        : undefined;

  // Fetch initial todos data on server
  const initialData = await getTodos(currentWorkspace.id, {
    page,
    pageSize: 20,
    completed
  });

  return (
    <div className="space-y-6 p-10">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Todos</h1>
        <p className="text-gray-600 mt-1">
          Manage your todos and track your progress
        </p>
      </div>

      {/* Create Todo Form */}
      <div className="bg-white p-6 rounded-lg border">
        <h2 className="text-lg font-semibold mb-4">Create New Todo</h2>
        <CreateTodoForm organizationId={currentWorkspace.id} />
      </div>

      {/* Todos List */}
      <div className="bg-white p-6 rounded-lg border">
        <h2 className="text-lg font-semibold mb-4">Your Todos</h2>
        <TodosList
          organizationId={currentWorkspace.id}
          initialData={initialData}
        />
      </div>
    </div>
  );
}

export default async function TodosPage({
  params,
  searchParams
}: TodosPageProps) {
  const { slug } = await params;
  const resolvedSearchParams = await searchParams;

  return (
    <WorkspaceGuard slug={slug}>
      <Suspense fallback={<TodosLoading />}>
        <TodosContent
          slug={slug}
          searchParams={resolvedSearchParams}
        />
      </Suspense>
    </WorkspaceGuard>
  );
}
