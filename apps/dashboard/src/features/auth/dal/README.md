# Authentication Data Access Layer (DAL)

This directory implements route-level authorization for authentication pages using the Data Access Layer (DAL) pattern as recommended in the Next.js documentation.

## Overview

The DAL pattern centralizes authentication and authorization logic, providing a clean separation between data access and presentation layers. This implementation protects sign-up and sign-in pages by redirecting already-authenticated users to appropriate destinations based on their status.

## Architecture

### Files Structure

```
apps/dashboard/src/features/auth/dal/
├── auth-dal.ts                 # Main DAL implementation
├── README.md                   # This documentation
```

### Components

```
apps/dashboard/src/features/auth/components/
├── auth-page-guard.tsx                # Route protection wrapper for auth pages
├── workspace-onboarding-guard.tsx     # Route protection wrapper for workspace onboarding
├── invite-onboarding-guard.tsx        # Route protection wrapper for invite onboarding
```

## Implementation Details

### AuthDAL Class

The `AuthDAL` class provides centralized authentication logic:

#### Key Methods

- **`getSessionData()`**: Retrieves complete session data including user info, organizations, and active organization
- **`checkAuthPageAccess()`**: Determines if authenticated users should be redirected from auth pages
- **`checkWorkspaceOnboardingAccess()`**: Determines if users should be redirected from workspace onboarding page
- **`checkInviteOnboardingAccess()`**: Determines if users should be redirected from invite onboarding page
- **`determineRedirectDestination()`**: Implements priority-based redirect logic
- **Utility methods**: `requiresEmailVerification()`, `hasCompletedOnboarding()`, `getOrganizationCount()`

#### Data Transfer Objects (DTOs)

- **`AuthUserDTO`**: Minimal user data for authorization decisions
- **`OrganizationDTO`**: Essential organization information
- **`AuthSessionDTO`**: Complete session context
- **`AuthorizationResult`**: Authorization decision result

### Route Protection Components

Server components that wrap pages and apply route protection:

#### AuthPageGuard Component

Protects authentication pages (sign-in/sign-up):

```tsx
export default async function SignInPage() {
  return (
    <AuthPageGuard>
      <AuthContainer maxWidth="md">
        <SignInForm />
      </AuthContainer>
    </AuthPageGuard>
  );
}
```

#### WorkspaceOnboardingGuard Component

Protects workspace onboarding page with requirements:

- User must be authenticated AND email verified
- Redirects if user has already completed onboarding
- Redirects to invite page if user has already created a workspace (status "workspace" or "invite")
- Redirects to invite page if user already has organizations

```tsx
export default async function WorkspaceOnboardingPage() {
  return (
    <WorkspaceOnboardingGuard>
      <AuthContainer maxWidth="md">
        <WorkspaceOnboardingForm />
      </AuthContainer>
    </WorkspaceOnboardingGuard>
  );
}
```

#### InviteOnboardingGuard Component

Protects invite onboarding page with requirements:

- User must be authenticated AND email verified AND have a workspace
- Redirects to workspace creation if no workspace exists
- Redirects if user has completed onboarding

```tsx
export default async function InviteOnboardingPage() {
  return (
    <InviteOnboardingGuard>
      <AuthContainer maxWidth="md">
        <InviteOnboardingForm />
      </AuthContainer>
    </InviteOnboardingGuard>
  );
}
```

## Authorization Logic Flow

### Authentication Pages (Sign-in/Sign-up)

When a user attempts to access sign-up or sign-in pages:

1. **Check Authentication**: Validate if user has a valid session
2. **If Not Authenticated**: Allow access to auth page
3. **If Authenticated**: Redirect based on priority:

### Onboarding Pages

#### Workspace Onboarding Page

When a user attempts to access the workspace onboarding page:

1. **Check Authentication**: Validate if user has a valid session
2. **If Not Authenticated**: Redirect to `/sign-in`
3. **If Authenticated**: Check email verification
4. **If Email Not Verified**: Redirect to `/verify`
5. **If Email Verified**: Check onboarding completion
6. **If Onboarding Complete**: Redirect to appropriate home page
7. **If Onboarding Status is "workspace" or "invite"**: Redirect to `/onboarding/invite` (user has already created workspace)
8. **If Onboarding Status is "incomplete"**: Check for existing organizations
9. **If User Has Organizations**: Redirect to `/onboarding/invite` (edge case handling)
10. **Otherwise**: Allow access to workspace creation

#### Invite Onboarding Page

When a user attempts to access the invite onboarding page:

1. **Check Authentication**: Validate if user has a valid session
2. **If Not Authenticated**: Redirect to `/sign-in`
3. **If Authenticated**: Check email verification
4. **If Email Not Verified**: Redirect to `/verify`
5. **If Email Verified**: Check onboarding completion
6. **If Onboarding Complete**: Redirect to appropriate home page
7. **If Onboarding Status is "invite" or "workspace"**: Allow access (handles timing issues after workspace creation)
8. **If Onboarding Status is "incomplete"**: Check workspace existence
9. **If No Workspace**: Redirect to `/onboarding/workspace`
10. **Otherwise**: Allow access to invite step

**Special Handling**: The guard includes logic to handle session synchronization timing issues that can occur immediately after workspace creation, where the user's onboarding status is updated but organization API calls might temporarily fail due to session propagation delays.

### Redirect Priority (in order)

1. **Granular Onboarding States**
   - `"incomplete"` → `/onboarding/workspace` (needs to create workspace)
   - `"workspace"` → `/onboarding/invite` (workspace created, needs to invite members)
   - `"invite"` → `/onboarding/invite` (invites sent, but not marked complete)
   - `"complete"` → Continue to next priority check

2. **Default Workspace** → `/{session.user.defaultWorkspace}/home`
   - User has a configured default workspace

3. **Email Verification** → `/verify`
   - `!session.user.emailVerified`

4. **No Organizations** → `/onboarding/workspace`
   - User has no organization memberships (fallback case)

5. **Active Organization** → `/{activeOrganization.slug}/home`
   - User has an active organization in their session

6. **First Organization** → `/{firstOrganization.slug}/home`
   - Use oldest organization (ordered by createdAt)

7. **Fallback** → `/`
   - Default redirect to root

## Key Features

### Performance Optimizations

- **React Cache**: Uses `cache()` to prevent duplicate API calls within the same request
- **Promise.allSettled**: Parallel API calls for organizations and active organization
- **Graceful Degradation**: Continues operation even if organization APIs fail

### Error Handling

- **Non-blocking Failures**: Organization API failures don't prevent session validation
- **Comprehensive Logging**: Detailed error logging for debugging
- **Fallback Behavior**: Safe defaults when data is unavailable
- **Session Timing Issues**: Special handling for post-workspace-creation timing issues where onboarding status is updated but organization APIs might temporarily return 401 errors

### Security Considerations

- **Minimal Data Exposure**: DTOs contain only necessary data for authorization
- **Server-side Validation**: All authorization logic runs on the server
- **Session Validation**: Uses existing `getBackendSession` utility for consistency

## Usage Examples

### Protecting Authentication Pages

```tsx
// apps/dashboard/src/app/(auth)/sign-in/page.tsx
import { AuthPageGuard } from "@/src/features/auth/components/auth-page-guard";

export default async function SignInPage() {
  return (
    <AuthPageGuard>
      <AuthContainer maxWidth="md">
        <SignInForm />
      </AuthContainer>
    </AuthPageGuard>
  );
}
```

### Using DAL Utilities

```tsx
// Check if user needs email verification
const needsVerification = await AuthDAL.requiresEmailVerification();

// Check onboarding status
const hasCompleted = await AuthDAL.hasCompletedOnboarding();
const needsWorkspace = await AuthDAL.needsWorkspaceCreation();
const needsInvites = await AuthDAL.needsMemberInvitation();

// Get organization count
const orgCount = await AuthDAL.getOrganizationCount();
```

## Integration Points

### Existing Systems

- **Session Management**: Uses `getBackendSession` from `@/src/features/auth/utils/session`
- **API Layer**: Integrates with `getUserOrganizations` and `getActiveOrganization`
- **Backend Validation**: Compatible with Hono backend session middleware

### Next.js Features

- **Server Components**: Runs authorization logic on the server
- **Redirect**: Uses Next.js `redirect()` for navigation
- **Caching**: Leverages React `cache()` for performance

## Maintenance

### Adding New Redirect Rules

1. Update `determineRedirectDestination()` method
2. Update this documentation

### Modifying DTOs

1. Update interface definitions in `auth-dal.ts`
2. Update transformation logic in `getSessionData()`

### Performance Monitoring

Monitor the following metrics:

- Session validation response times
- Organization API call success rates
- Cache hit rates for session data
- Redirect accuracy and user experience

## Dependencies

- `next/navigation`: For server-side redirects
- `react`: For caching functionality
- `@/src/features/auth/utils/session`: Session validation
- `@/src/lib/api/onboarding`: Organization data APIs

This implementation provides a robust, scalable, and maintainable solution for route-level authorization that follows Next.js best practices and integrates seamlessly with the existing authentication system.
