"use client";

import React, { useState } from "react";
import <PERSON> from "next/link";
import { usePathname } from "next/navigation";
import homeAnimationData from "@/src/features/animated-icons/components/home-animated.json";
import inboxAnimationData from "@/src/features/animated-icons/components/inbox-animated.json";
import knowledgeAnimationData from "@/src/features/animated-icons/components/knowledge-animated.json";
import settingsAnimationData from "@/src/features/animated-icons/components/settings-animated.json";
import trashAnimationData from "@/src/features/animated-icons/components/trash-animated.json";
import {
  SidebarGroup,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem
} from "@repo/ui/components/sidebar";

import AnimatedIcon from "../../animated-icons/components/animated-icon";

interface NavItemType {
  title: string;
  url: string;
  icon?: React.ElementType;
  animationData?: unknown;
}

const navMain: NavItemType[] = [
  {
    title: "Home",
    url: "/home",
    animationData: homeAnimationData
  },
  {
    title: "Docs",
    url: "/docs",
    animationData: knowledgeAnimationData
  },
  {
    title: "Inbox",
    url: "/inbox",
    animationData: inboxAnimationData
  },
  {
    title: "Archive",
    url: "/archive",
    animationData: trashAnimationData
  },
  {
    title: "Settings",
    url: "/settings/general",
    animationData: settingsAnimationData
  }
];

export default function SidebarNavMain() {
  const pathname = usePathname();
  const loading = false;
  // const { slug, loading } = useWorkspace();
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);

  return (
    <SidebarGroup>
      <SidebarMenu>
        {navMain.map((item) => {
          // const href = `/${slug}${item.url}`;
          const isActive = pathname === item.url;

          return (
            <SidebarMenuItem key={item.title}>
              <SidebarMenuButton
                asChild={!loading}
                isActive={isActive}
                onMouseEnter={() => setHoveredItem(item.title)}
                onMouseLeave={() => setHoveredItem(null)}
              >
                {!loading ? (
                  <Link
                    prefetch
                    href={item.url}
                  >
                    {item.animationData ? (
                      <AnimatedIcon
                        animationData={item.animationData}
                        isHovered={hoveredItem === item.title}
                      />
                    ) : null}
                    <span>{item.title}</span>
                  </Link>
                ) : (
                  <>
                    {item.animationData ? (
                      <AnimatedIcon
                        animationData={item.animationData}
                        isHovered={hoveredItem === item.title}
                      />
                    ) : null}
                    <span>{item.title}</span>
                  </>
                )}
              </SidebarMenuButton>
            </SidebarMenuItem>
          );
        })}
      </SidebarMenu>
    </SidebarGroup>
  );
}
