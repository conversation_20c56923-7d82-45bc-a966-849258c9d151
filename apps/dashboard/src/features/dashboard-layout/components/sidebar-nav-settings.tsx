"use client";

import React, { useState } from "react";
import <PERSON> from "next/link";
import { usePathname } from "next/navigation";
import billingAnimationData from "@/src/features/animated-icons/components/billing-animated.json";
import generalAnimationData from "@/src/features/animated-icons/components/general-animated.json";
import notificationsAnimationData from "@/src/features/animated-icons/components/notifications-animated.json";
import profileAnimationData from "@/src/features/animated-icons/components/profile-animated.json";
import referAnimationData from "@/src/features/animated-icons/components/refer-animated.json";
import securityAnimationData from "@/src/features/animated-icons/components/security-animated.json";
import teamAnimationData from "@/src/features/animated-icons/components/team-animated.json";
import {
  SidebarGroup,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem
} from "@repo/ui/components/sidebar";

import AnimatedIcon from "../../animated-icons/components/animated-icon";

interface NavItemType {
  title: string;
  url: string;
  icon?: React.ElementType;
  animationData?: unknown;
}

const navSettings: NavItemType[] = [
  {
    title: "General",
    url: "/settings/general",
    animationData: generalAnimationData
  },
  {
    title: "Profile",
    url: "/settings/profile",
    animationData: profileAnimationData
  },
  {
    title: "Members",
    url: "/settings/members",
    animationData: teamAnimationData
  },
  {
    title: "Billing",
    url: "/settings/billing",
    animationData: billingAnimationData
  },
  {
    title: "Security",
    url: "/settings/security",
    animationData: securityAnimationData
  },
  {
    title: "Notifications",
    url: "/settings/notifications",
    animationData: notificationsAnimationData
  },
  {
    title: "Referrals",
    url: "/settings/referral",
    animationData: referAnimationData
  }
];

export default function SidebarNavSettings() {
  const pathname = usePathname();
  const loading = false;
  // const { slug, loading } = useWorkspace();
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);

  return (
    <SidebarGroup>
      <SidebarMenu>
        {navSettings.map((item) => {
          // const href = `/${slug}${item.url}`;
          const isActive = pathname === item.url;

          return (
            <SidebarMenuItem key={item.title}>
              <SidebarMenuButton
                asChild={!loading}
                isActive={isActive}
                onMouseEnter={() => setHoveredItem(item.title)}
                onMouseLeave={() => setHoveredItem(null)}
              >
                {!loading ? (
                  <Link
                    prefetch
                    href={item.url}
                  >
                    {item.animationData ? (
                      <AnimatedIcon
                        animationData={item.animationData}
                        isHovered={hoveredItem === item.title}
                      />
                    ) : null}
                    <span>{item.title}</span>
                  </Link>
                ) : (
                  <>
                    {item.animationData ? (
                      <AnimatedIcon
                        animationData={item.animationData}
                        isHovered={hoveredItem === item.title}
                      />
                    ) : null}
                    <span>{item.title}</span>
                  </>
                )}
              </SidebarMenuButton>
            </SidebarMenuItem>
          );
        })}
      </SidebarMenu>
    </SidebarGroup>
  );
}
