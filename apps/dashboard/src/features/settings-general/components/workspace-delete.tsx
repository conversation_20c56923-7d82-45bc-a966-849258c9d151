"use client";

import { useState } from "react";
import { useOptimizedWorkspace } from "@/src/features/auth/providers/session-provider";
import { Button } from "@repo/ui/components/button";
import { Trash2 } from "lucide-react";

import { DeleteWorkspaceModal } from "./delete-workspace-modal";

export default function WorkspaceDelete() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { workspace: currentWorkspace } = useOptimizedWorkspace();

  // Handle loading state
  if (!currentWorkspace) {
    return null;
  }

  // Only show delete option for workspace owners
  // This should be handled at the page level, but adding here as safety
  const userRole = currentWorkspace.userRole;
  if (userRole !== "owner") {
    return null;
  }

  return (
    <>
      <div className="flex gap-6 w-full items-center justify-between border rounded-2xl p-6">
        <div className="flex flex-col gap-1 w-2/5">
          <p className="text-sm font-medium">Delete Workspace</p>
          <p className="text-sm text-muted-foreground">
            Delete your workspace and all associated data. This action is
            irreversible.
          </p>
        </div>
        <div className="flex flex-col gap-4 w-3/5 items-end">
          <Button
            variant="destructive"
            size="sm"
            className="w-fit"
            onClick={() => setIsModalOpen(true)}
          >
            <Trash2 className="w-4 h-4" />
            <span>Delete Workspace</span>
          </Button>
        </div>
      </div>

      <DeleteWorkspaceModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        organizationSlug={currentWorkspace.slug || ""}
      />
    </>
  );
}
