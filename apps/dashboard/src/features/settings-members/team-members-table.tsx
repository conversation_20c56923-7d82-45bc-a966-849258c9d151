"use client";

import React, {
  use<PERSON><PERSON>back,
  useEffect,
  useMemo,
  useRef,
  useState
} from "react";
import { useParams } from "next/navigation";
import { useOptimizedSession } from "@/src/features/auth/providers/session-provider";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger
} from "@repo/ui/components/alert-dialog";
import { Badge } from "@repo/ui/components/badge";
import { Button } from "@repo/ui/components/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@repo/ui/components/dropdown-menu";
import {
  AllCommunityModule,
  iconSetQuartzLight,
  ModuleRegistry,
  themeQuartz
} from "ag-grid-community";
import type { ColDef, IRowNode } from "ag-grid-community";
import { AgGridReact } from "ag-grid-react";
import {
  Copy,
  EllipsisVertical,
  Mail,
  Repeat,
  UserCheck,
  UserLock,
  X
} from "lucide-react";

import { useMemberManagement, useMembers } from "./hooks/use-members";
import type { UseTeamMembersFilterReturn } from "./hooks/use-team-members-filter";
import type { Member, MembersResponse } from "./types/member-types";

// Use Member type from API layer
type MemberItem = Member & {
  // Add any UI-specific fields if needed
};

// Status is now computed server-side, no need for client-side derivation

const TableTheme = themeQuartz.withPart(iconSetQuartzLight).withParams({
  accentColor: "#6363F1",
  browserColorScheme: "light",
  headerFontWeight: "normal",
  rowHoverColor: "#F9F9FA",
  rangeSelectionBorderStyle: "none",
  rangeSelectionBorderColor: "transparent",
  selectedRowBackgroundColor: "#F9F9FA",
  headerHeight: 40,
  headerFontSize: 13,
  headerRowBorder: true,
  rowBorder: true,
  spacing: 10,
  wrapperBorderRadius: 10
});

// Status is computed server-side using deriveMemberStatus function

// Custom cell renderer for name and email combination
function NameEmailCell({ data }: { data: MemberItem }) {
  if (!data) return null;

  // Get current user to show "(you)" indicator
  const { data: currentUser } = useOptimizedSession();
  const isCurrentUser = data.user.id === currentUser?.user?.id;

  // For pending invitations (name is null), show only email
  // For active members, show name and email
  return (
    <div className="flex h-full w-full items-center gap-2 truncate">
      {data.user.name ? (
        <>
          <span className="font-medium text-sm">
            {data.user.name}
            {isCurrentUser && <span className="font-normal"> (You)</span>}
          </span>
          <span className="text-muted-foreground truncate">
            {data.user.email}
          </span>
        </>
      ) : (
        <span className="font-medium text-sm">
          {data.user.email}
          {isCurrentUser && (
            <span className="text-muted-foreground font-normal"> (you)</span>
          )}
        </span>
      )}
    </div>
  );
}

// Custom cell renderer for status badge
function StatusBadgeCell({ data }: { data: MemberItem }) {
  if (!data) return null;

  // Fallback to "active" if status is not provided (for legacy members)
  const status = data.status || "active";

  const getStatusVariant = (status: string) => {
    switch (status) {
      case "active":
        return "grey";
      case "pending":
        return "blue";
      case "suspended":
        return "red";
      default:
        return "grey";
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case "active":
        return "Active";
      case "pending":
        return "Pending";
      case "suspended":
        return "Suspended";
      default:
        return "Unknown";
    }
  };

  return (
    <div className="flex h-full w-full items-center justify-end">
      <Badge
        variant={getStatusVariant(status)}
        className="text-xs"
      >
        {getStatusLabel(status)}
      </Badge>
    </div>
  );
}

function RoleBadgeCell({ data }: { data: MemberItem }) {
  if (!data) return null;

  return (
    <div className="flex h-full w-full items-center">
      <Badge
        className="text-xs"
        variant="grey"
      >
        {data.role}
      </Badge>
    </div>
  );
}

export const getColumnDefinitions = (): ColDef<MemberItem>[] => [
  {
    field: "user.name" as keyof MemberItem,
    headerName: "Member",
    minWidth: 300,
    resizable: false,
    suppressMovable: true,
    cellRenderer: NameEmailCell,
    // Include both name and email in quick filter search
    getQuickFilterText: (params) => {
      return `${params.data?.user?.name || ""} ${params.data?.user?.email || ""}`;
    }
  },
  {
    field: "role",
    headerName: "Role",
    resizable: false,
    flex: 1,
    minWidth: 130,
    suppressMovable: true,
    cellRenderer: RoleBadgeCell
  },
  {
    field: "status",
    headerName: "",
    resizable: false,
    width: 130,
    suppressMovable: true,
    cellRenderer: StatusBadgeCell
  },

  {
    field: undefined,
    headerName: "",
    width: 50,
    resizable: false,
    cellRenderer: (params: { data: MemberItem }) => (
      <RowActions data={params.data} />
    ),
    suppressMovable: true
  }
];

// Register AG Grid modules
ModuleRegistry.registerModules([AllCommunityModule]);

interface TeamMembersTableProps {
  initialData?: MembersResponse;
  filterHook?: UseTeamMembersFilterReturn;
}

export default function TeamMembersTable({
  initialData,
  filterHook
}: TeamMembersTableProps = {}) {
  const params = useParams();
  const organizationSlug = params?.slug as string;
  const gridRef = useRef<AgGridReact>(null);

  const columnDefs = useMemo(() => getColumnDefinitions(), []);

  // Use React Query to fetch members data
  // Note: We fetch ALL members and do filtering client-side to support multiple status filters
  const { data: membersData, isLoading } = useMembers(
    organizationSlug,
    {
      search: filterHook?.searchText
      // Removed status filter - we'll handle this client-side for better multi-select support
    },
    initialData
  );

  const rowData = membersData?.members || [];

  // Apply quick filter when search text changes and manage overlay
  useEffect(() => {
    if (filterHook && gridRef.current?.api) {
      filterHook.applyQuickFilter(gridRef);

      // Check if we need to show overlay after filtering
      setTimeout(() => {
        if (gridRef.current?.api) {
          const displayedRowCount = gridRef.current.api.getDisplayedRowCount();
          const hasSearchFilter =
            filterHook.searchText && filterHook.searchText.length > 0;

          if (displayedRowCount === 0 && hasSearchFilter) {
            gridRef.current.api.showNoRowsOverlay();
          } else {
            gridRef.current.api.hideOverlay();
          }
        }
      }, 0);
    }
  }, [filterHook]);

  // External filter functions for status filtering
  const isExternalFilterPresent = useCallback(() => {
    return Boolean(
      filterHook?.selectedStatuses && filterHook.selectedStatuses.length > 0
    );
  }, [filterHook?.selectedStatuses]);

  const doesExternalFilterPass = useCallback(
    (node: IRowNode<MemberItem>) => {
      if (
        !filterHook?.selectedStatuses ||
        filterHook.selectedStatuses.length === 0
      ) {
        return true; // No status filter applied, pass all rows
      }

      const rowStatus = node.data?.status;
      return rowStatus
        ? filterHook.selectedStatuses.includes(rowStatus)
        : false;
    },
    [filterHook?.selectedStatuses]
  );

  // Handle grid events for overlay management
  const onFilterChanged = () => {
    if (gridRef.current?.api) {
      const displayedRowCount = gridRef.current.api.getDisplayedRowCount();
      const hasActiveFilters = filterHook?.hasActiveFilters;

      if (displayedRowCount === 0 && hasActiveFilters) {
        gridRef.current.api.showNoRowsOverlay();
      } else {
        gridRef.current.api.hideOverlay();
      }
    }
  };

  // Apply external filter when status selection changes
  useEffect(() => {
    if (gridRef.current?.api) {
      gridRef.current.api.onFilterChanged();
    }
  }, [filterHook?.selectedStatuses]);

  return (
    <div className="w-full h-full">
      <AgGridReact
        ref={gridRef}
        rowData={rowData}
        columnDefs={columnDefs as ColDef<MemberItem>[]}
        loading={isLoading}
        quickFilterText={filterHook?.searchText || ""}
        overlayNoRowsTemplate="No members or invites match that filter"
        onFilterChanged={onFilterChanged}
        isExternalFilterPresent={isExternalFilterPresent}
        doesExternalFilterPass={doesExternalFilterPass}
        // Other configurations
        suppressCellFocus={true}
        pagination={true}
        paginationPageSizeSelector={false}
        paginationPageSize={8}
        // Ensure table fills container
        domLayout="autoHeight"
        theme={TableTheme}
      />
    </div>
  );
}

function RowActions({ data }: { data: MemberItem }) {
  const [isOpen, setIsOpen] = useState(false);
  const params = useParams();
  const organizationSlug = params?.slug as string;

  const { updateMember, removeMember, resendInvitation, transferOwnership } =
    useMemberManagement(organizationSlug);

  // Get current user and find their role in this organization from members data
  const { data: currentUser } = useOptimizedSession();
  const { data: membersData } = useMembers(organizationSlug);

  const currentUserMembership = membersData?.members?.find(
    (member) => member.user.id === currentUser?.user?.id
  );
  const currentUserRole = currentUserMembership?.role;
  const isCurrentUserOwner = currentUserRole === "owner";

  if (!data) return null;

  const isPendingInvite = data.status === "pending";
  const isActive = data.status === "active";
  const isSuspended = data.status === "suspended";
  const isOwner = data.role === "owner";
  const isAdmin = data.role === "admin";

  // Check if this is the current user or if they're an owner (can't manage themselves or owners)
  const isCurrentUser = data.user.id === currentUser?.user?.id;
  const shouldDisableDropdown = isOwner || isCurrentUser;

  const handleResendInvite = async () => {
    if (!data.user.email || !data.role) {
      return;
    }

    resendInvitation.mutate({
      email: data.user.email,
      role: data.role as "member" | "admin"
    });
  };

  const handleRevokeInvite = () => {
    removeMember.mutate(data.id);
  };

  const handleMakeAdmin = () => {
    updateMember.mutate({
      memberId: data.id,
      data: { role: "admin" }
    });
  };

  const handleSuspend = () => {
    updateMember.mutate({
      memberId: data.id,
      data: { ban: true }
    });
  };

  const handleReactivate = () => {
    updateMember.mutate({
      memberId: data.id,
      data: { ban: false }
    });
  };

  const handleTransferOwnership = () => {
    transferOwnership.mutate(data.id);
  };

  return (
    <DropdownMenu onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <div className="flex justify-end items-center h-full">
          <Button
            size="iconXs"
            variant="ghost"
            disabled={shouldDisableDropdown}
            className={`shadow-none ${
              shouldDisableDropdown
                ? "cursor-not-allowed opacity-50"
                : `hover:bg-neutral-200/80 ${isOpen ? "bg-neutral-200/80" : ""}`
            }`}
          >
            <EllipsisVertical
              size={15}
              aria-hidden="true"
              className={
                shouldDisableDropdown
                  ? "text-muted-foreground/50"
                  : "text-muted-foreground"
              }
            />
          </Button>
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {/* Actions for users with pending invites */}
        {isPendingInvite && (
          <DropdownMenuGroup>
            <DropdownMenuItem
              onClick={handleResendInvite}
              disabled={resendInvitation.isPending}
            >
              <Mail className="size-4" />
              <span>Resend invite</span>
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Copy className="size-4" />
              <span>Copy invite link</span>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              className="text-destructive focus:text-destructive"
              onClick={handleRevokeInvite}
            >
              <X className="size-4 text-destructive" />
              <span>Revoke invite</span>
            </DropdownMenuItem>
          </DropdownMenuGroup>
        )}

        {/* Actions for active users */}
        {isActive && !isOwner && (
          <DropdownMenuGroup>
            {!isAdmin && (
              <DropdownMenuItem onClick={handleMakeAdmin}>
                <UserLock className="size-4" />
                <span>Make admin</span>
              </DropdownMenuItem>
            )}
            {/* Show transfer ownership option only if current user is owner */}
            {isCurrentUserOwner && (
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                    <Repeat className="size-4" />
                    <span>Transfer ownership</span>
                  </DropdownMenuItem>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Transfer Ownership</AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to transfer ownership to{" "}
                      <strong>{data.user.name || data.user.email}</strong>?
                      <br />
                      This action is permanent and cannot be undone. You will
                      become an admin and {data.user.name ||
                        data.user.email}{" "}
                      will become the organization owner.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={handleTransferOwnership}
                      disabled={transferOwnership.isPending}
                    >
                      {transferOwnership.isPending
                        ? "Transferring..."
                        : "Transfer Ownership"}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            )}
            <DropdownMenuItem
              className="text-destructive focus:text-destructive"
              onClick={handleSuspend}
            >
              <X className="size-4 text-destructive" />
              <span>Suspend</span>
            </DropdownMenuItem>
          </DropdownMenuGroup>
        )}

        {/* Actions for suspended users */}
        {isSuspended && (
          <DropdownMenuGroup>
            <DropdownMenuItem onClick={handleReactivate}>
              <UserCheck className="size-4" />
              <span>Reactivate user</span>
            </DropdownMenuItem>
          </DropdownMenuGroup>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
