# Auth Package Overview

This package provides a comprehensive authentication system built on **Auth.js v5** (next-auth 5.0.0-beta.25), replacing the previous better-auth implementation. It offers a modular, type-safe authentication solution with support for multiple providers, 2FA, email verification, and advanced security features.

## Architecture Overview

The auth package is structured as a modular system with clearly separated concerns:

- **Core Authentication**: Main Auth.js configuration and session management
- **Provider System**: Multiple authentication providers including credentials, TOTP, and OAuth
- **Security Layer**: Encryption, rate limiting, and validation
- **Database Integration**: Prisma adapter for data persistence
- **Email Integration**: Verification and notification system
- **Organization Management**: Role-based access control and permissions

## File Structure and Descriptions

### Core Configuration Files

#### `src/index.ts`

**Main entry point and Auth.js configuration**

- Exports the primary `NextAuth` instance with complete configuration
- Defines `authConfig` object with all authentication settings
- Provides main exports: `handlers`, `auth`, `signIn`, `signOut`
- Includes `dedupedAuth` for cached server-side authentication calls
- Configures custom pages, JWT encoding, and session handling

**Key Features:**

- 30-day session expiry with 24-hour update intervals
- Custom JWT encoding for credentials sessions
- Trusted host configuration for deployment flexibility

#### `src/session.ts`

**Session management and validation**

- `checkSession()`: Comprehensive session validation with type guards
- `generateSessionToken()`: Creates ULID-based session tokens
- `getSessionExpiryFromNow()`: Calculates session expiration dates
- Session configuration with database strategy

**Security Features:**

- ULID validation for user IDs
- Comprehensive session field validation
- Type-safe session checking with detailed error logging

#### `src/context.ts`

**Authentication context providers for different use cases**

- `getAuthContextForAuth()`: For authentication routes (allows incomplete onboarding)
- `getAuthContext()`: For protected routes (requires complete onboarding)
- `getAuthOrganizationContext()`: For organization-specific routes
- Cached database operations for performance optimization

**Features:**

- Automatic redirect handling based on user state
- User info enrichment with organization data
- Onboarding status enforcement
- Role-based context enrichment

### Provider System

#### `src/providers.ts`

**Authentication provider configurations**

Implements four credential providers:

1. **Credentials Provider** (`Provider.Credentials`)
   - Username/password authentication
   - Email verification enforcement
   - Rate limiting protection
   - Redirects to TOTP after successful login

2. **TOTP Code Provider** (`Provider.TotpCode`)
   - Time-based One-Time Password authentication
   - Encrypted token validation with expiry
   - TOTP secret decryption and validation
   - Rate limiting and security checks

3. **Recovery Code Provider** (`Provider.RecoveryCode`)
   - Backup authentication using recovery codes
   - One-time use code consumption
   - Encrypted recovery code storage
   - Automatic code deletion after use

4. **Email Verification Provider** (`Provider.EmailVerification`)
   - Token-based email verification
   - JWT-like token validation
   - Automatic email verification on success

**Security Features:**

- Built-in rate limiting (10 requests per minute)
- Symmetric encryption for sensitive data
- Token expiration validation
- Comprehensive error handling

#### `src/providers.types.ts`

**Provider enumeration**

Defines all supported authentication providers:

- `Credentials`, `TotpCode`, `RecoveryCode`
- `EmailVerification`

### Security and Utilities

#### `src/encryption.ts`

**Symmetric encryption utilities**

- `symmetricEncrypt()`: AES-256 encryption with random IV
- `symmetricDecrypt()`: Corresponding decryption function
- SHA-256 key derivation for consistent key length
- Hex encoding for storage compatibility

**Security Implementation:**

- AES-256 encryption algorithm
- Random 16-byte initialization vectors
- SHA-256 key derivation from AUTH_SECRET
- Secure encoding/decoding patterns

#### `src/cookies.ts`

**Cookie management and base URL configuration**

- Secure cookie naming based on HTTPS detection
- Environment-specific base URL determination
- Auth.js cookie constants (`SessionToken`, `CsrfToken`, `CallbackUrl`)

**Features:**

- `__Secure-` prefixes for HTTPS environments
- `__Host-` prefixes for maximum security
- Vercel deployment support
- Local development configuration

#### `src/constants.ts`

**Security and timing constants**

- `BCRYPT_SALT_LENGTH`: 13 rounds for password hashing
- `MINIMUM_PASSWORD_LENGTH`: 8 characters
- Email verification expiry: 30 days
- Password reset expiry: 6 hours
- TOTP/recovery code expiry: 12 minutes

#### `src/in-memory.ts`

**Rate limiting implementation**

- `inMemoryRateLimiter()`: Memory-based rate limiting
- Configurable intervals and request limits
- Automatic cleanup and expiration
- Returns rate limit status and remaining requests

**Note:** Not suitable for serverless environments due to in-memory storage.

### Validation and Types

#### `src/schemas.ts`

**Zod validation schemas**

- `logInSchema`: Email/password validation (255 char limit, email format)
- `submitTotpCodeSchema`: TOTP submission validation (6-digit codes)
- `submitRecoveryCodeSchema`: Recovery code validation (11 char limit)

**Validation Features:**

- Comprehensive error messages
- String trimming and length limits
- Email format validation
- Type inference for TypeScript integration

#### `src/type-guards.ts`

**Runtime type validation utilities**

- `isDefined()`: Null/undefined checks with type narrowing
- `isString()`: String type validation
- `IsDefinedGuard<T>`: Type utility for non-nullable types

#### `src/maybe.ts`

**Optional type utility**

- `Maybe<T>`: Union type for nullable values (`T | undefined | null`)

### Feature Modules

#### `src/password.ts`

**Password management system**

- `hashPassword()`: bcrypt hashing with configurable salt rounds
- `verifyPassword()`: Secure password comparison
- `PasswordValidator` class with validation rules:
  - Mixed case requirement
  - Minimum length enforcement
  - Number requirement
  - Comprehensive validation with error collection

#### `src/verification.ts`

**Email verification system**

- `createOtpTokens()`: Generates 3-character OTP codes with expiry
- `findVerificationTokenFromOtp()`: Validates and retrieves OTP tokens
- `verifyEmail()`: Completes email verification process
- Uses web-compatible crypto APIs for hash generation

**Security Features:**

- SHA-256 hashed OTP storage
- 30-day expiration periods
- Token cleanup on verification
- Web crypto API compatibility

#### `src/invitations.ts`

**Organization invitation system**

- `checkIfCanInvite()`: Validates invitation eligibility
- `createInvitation()`: Creates new invitations with role assignment
- `sendInvitationRequest()`: Sends invitation emails
- Automatic revocation of existing pending invitations

**Features:**

- Role-based invitation system
- Email template integration
- Invitation state management
- Duplicate prevention

#### `src/permissions.ts`

**Role-based access control**

Organization permission checking functions:

- `isOrganizationOwner()`: Owner role validation
- `isOrganizationAdmin()`: Admin role validation
- `isOrganizationContributor()`: Contributor role validation
- `isOrganizationViewer()`: Viewer role validation

All functions throw `NotFoundError` for invalid memberships.

### System Integration

#### `src/adapter.ts`

**Database adapter configuration**

- Uses `@auth/prisma-adapter` for Prisma integration
- Frozen adapter object to prevent modifications
- Direct integration with shared database package
- Designed for stability across Auth.js updates

#### `src/callbacks.ts`

**Auth.js lifecycle callbacks**

- `signIn`: Handles different provider types and creates sessions
- `jwt`: Manages JWT token lifecycle and session creation
- `session`: Enriches session data with user information
- Custom session token handling for credentials providers

**Special Features:**

- TOTP redirect after credentials authentication
- Session token management for credentials flows
- Prevents client-side session updates
- OAuth profile name truncation (64 char limit)

#### `src/events.ts`

**Authentication event handlers**

- `signIn`: Updates last login time, handles new user welcome emails
- `signOut`: Cleans up database sessions
- `linkAccount`: Sends security alerts for account linking

**Email Integration:**

- Welcome emails for new users
- Security alerts for account connections
- Integration with email package templates

#### `src/errors.ts`

**Comprehensive error system**

Custom error classes extending `CredentialsSignin`:

- `InternalServerError`, `IncorrectEmailOrPasswordError`
- `TotpCodeRequiredError`, `IncorrectTotpCodeError`
- `MissingRecoveryCodesError`, `IncorrectRecoveryCodeError`
- `UnverifiedEmailError`, `RequestExpiredError`
- `RateLimitExceededError`

Plus standalone `NotFoundError` for database operations.

#### `src/redirect.ts`

**Navigation and redirect utilities**

- `getRedirectToSignIn()`: Constructs sign-in URLs with callbacks
- `getRedirectAfterSignIn()`: Determines post-authentication destinations
- `getRedirectToTotp()`: Creates TOTP verification URLs with encrypted tokens
- `getRequestStoragePathname()`: Extracts current request pathname

**Features:**

- Callback URL preservation
- Encrypted token generation for TOTP flow
- Next.js pathname handling
- Base path support

## Dependencies and External Integrations

### Key Dependencies

- **next-auth**: 5.0.0-beta.25 (Auth.js v5)
- **@auth/prisma-adapter**: Database integration
- **@otplib/core + plugins**: TOTP implementation
- **bcryptjs**: Password hashing
- **date-fns**: Date manipulation
- **ulid**: Session token generation
- **zod**: Schema validation

### Internal Package Dependencies

- **@repo/database**: Prisma client and models
- **@repo/email**: Email templates and sending

### Environment Variables

- `AUTH_SECRET`: Master encryption key for tokens and sensitive data
- `VERCEL_ENV`: Environment detection
- `VERCEL_URL`: Dynamic URL generation for Vercel deployments

## Package Exports

The package provides granular exports for maximum flexibility:

```typescript
// Main auth instance
import { auth, handlers, signIn, signOut } from "@repo/auth";
// Context providers
import { getAuthContext } from "@repo/auth/context";
import { symmetricEncrypt } from "@repo/auth/encryption";
// Error handling
import { AuthErrorCode } from "@repo/auth/errors";
// Utilities
import { passwordValidator } from "@repo/auth/password";
// Schemas
import { logInSchema } from "@repo/auth/schemas";
```

## Security Considerations

### Encryption and Hashing

- **Password Hashing**: bcrypt with 13 salt rounds
- **Token Encryption**: AES-256 with random IVs
- **OTP Hashing**: SHA-256 with secret key
- **Session Tokens**: ULID-based for unpredictability

### Rate Limiting

- In-memory rate limiter (10 requests/minute per identifier)
- Applied to all authentication attempts
- Automatic cleanup and expiration

### Session Security

- 30-day expiration with 24-hour refresh
- Database-stored sessions for server-side validation
- Secure cookie configuration based on HTTPS
- Session invalidation on sign-out

### Token Management

- Short-lived tokens for TOTP/recovery (12 minutes)
- Long-lived email verification (30 days)
- Encrypted storage of sensitive tokens
- Automatic cleanup on use/expiry

## Migration from Better-Auth

This package represents a complete replacement of the better-auth system with several improvements:

### Enhanced Security

- Stronger encryption algorithms
- Better rate limiting
- Improved session management
- More comprehensive error handling

### Better TypeScript Integration

- Full type safety throughout
- Zod schema validation
- Runtime type guards
- Improved developer experience

### Modular Architecture

- Clear separation of concerns
- Individual module exports
- Easier testing and maintenance
- Better code organization

### Auth.js Ecosystem Benefits

- Better Next.js integration
- Standard OAuth provider support
- Community-maintained adapters
- Regular security updates
