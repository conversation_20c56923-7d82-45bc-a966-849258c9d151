import { Resend } from "resend";

import { resendOptions } from "./resend-options";
import { MailerPayload } from "./types";

export async function sendEmail(payload: MailerPayload): Promise<unknown> {
  const mailer = new Resend(process.env.RESEND_API_KEY!);
  return mailer.emails.send({
    from: resendOptions.from,
    to: payload.recipient,
    subject: payload.subject,
    html: payload.html,
    text: payload.text,
  });
}
